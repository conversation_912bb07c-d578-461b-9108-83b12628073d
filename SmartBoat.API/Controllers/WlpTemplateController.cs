using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using System.ComponentModel.DataAnnotations;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace SmartBoat.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class WlpTemplateController : ControllerBase
    {
        private readonly IWlpTemplateService _wlpTemplateService;
        private readonly ILogger<WlpTemplateController> _logger;

        public WlpTemplateController(
            IWlpTemplateService wlpTemplateService,
            ILogger<WlpTemplateController> logger)
        {
            _wlpTemplateService = wlpTemplateService;
            _logger = logger;
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateTemplate([FromBody] CreateWlpTemplateRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetUserIdFromToken();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("User ID not found in token");
                }

                // Convert base64 file content to bytes
                byte[] fileContent;
                try
                {
                    fileContent = Convert.FromBase64String(request.Payload.FileContentBase64);
                }
                catch (FormatException)
                {
                    return BadRequest("Invalid file content format. Expected base64 string.");
                }

                var createDto = new CreateWlpTemplateDto
                {
                    Name = request.Payload.Name,
                    Description = request.Payload.Description,
                    FileName = request.Payload.FileName,
                    FileContent = fileContent
                };

                var result = await _wlpTemplateService.CreateTemplateAsync(createDto, userId);

                return Ok(new { success = true, data = result });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation creating WLP template");
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating WLP template");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("list")]
        public async Task<IActionResult> GetTemplates([FromBody] WlpTemplateListApiRequestDto request)
        {
            try
            {
                var userId = GetUserIdFromToken();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("User ID not found in token");
                }

                var listRequest = new WlpTemplateListRequestDto
                {
                    SearchTerm = request.Payload.SearchTerm,
                    FromDate = request.Payload.FromDate,
                    ToDate = request.Payload.ToDate,
                    PageOffset = request.Payload.PageOffset,
                    PageLimit = request.Payload.PageLimit
                };

                var result = await _wlpTemplateService.GetTemplatesAsync(listRequest, userId);

                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting WLP templates");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("get")]
        public async Task<IActionResult> GetTemplate([FromBody] GetWlpTemplateRequestDto request)
        {
            try
            {
                var userId = GetUserIdFromToken();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("User ID not found in token");
                }

                var result = await _wlpTemplateService.GetTemplateByIdAsync(request.Payload.TemplateId, userId);
                
                if (result == null)
                {
                    return NotFound(new { success = false, message = "Template not found" });
                }

                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting WLP template");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("update")]
        public async Task<IActionResult> UpdateTemplate([FromBody] UpdateWlpTemplateApiRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetUserIdFromToken();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("User ID not found in token");
                }

                var updateDto = new UpdateWlpTemplateDto
                {
                    Id = request.Payload.Id,
                    Name = request.Payload.Name,
                    Description = request.Payload.Description
                };

                var result = await _wlpTemplateService.UpdateTemplateAsync(updateDto, userId);

                return Ok(new { success = true, data = result });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid argument updating WLP template");
                return NotFound(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating WLP template");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("delete")]
        public async Task<IActionResult> DeleteTemplate([FromBody] DeleteWlpTemplateRequestDto request)
        {
            try
            {
                var userId = GetUserIdFromToken();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("User ID not found in token");
                }

                var success = await _wlpTemplateService.DeleteTemplateAsync(request.Payload.TemplateId, userId);
                
                if (!success)
                {
                    return NotFound(new { success = false, message = "Template not found" });
                }

                return Ok(new { success = true, message = "Template deleted successfully" });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation deleting WLP template");
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting WLP template");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("polygons")]
        public async Task<IActionResult> GetTemplatePolygons([FromBody] GetWlpTemplatePolygonsRequestDto request)
        {
            try
            {
                var userId = GetUserIdFromToken();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("User ID not found in token");
                }

                var result = await _wlpTemplateService.GetTemplatePolygonsAsync(request.Payload.TemplateId, userId);

                return Ok(new { success = true, data = result });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid argument getting template polygons");
                return NotFound(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting template polygons");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("check-duplicate")]
        public async Task<IActionResult> CheckDuplicate([FromBody] CheckDuplicateWlpTemplateRequestDto request)
        {
            try
            {
                var userId = GetUserIdFromToken();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("User ID not found in token");
                }

                // Convert base64 to bytes and calculate hash
                byte[] fileContent;
                try
                {
                    fileContent = Convert.FromBase64String(request.Payload.FileContentBase64);
                }
                catch (FormatException)
                {
                    return BadRequest("Invalid file content format. Expected base64 string.");
                }

                var hash = System.Security.Cryptography.SHA256.HashData(fileContent);
                var hashString = Convert.ToHexString(hash);

                var duplicate = await _wlpTemplateService.FindDuplicateTemplateAsync(hashString, userId);

                return Ok(new { 
                    success = true, 
                    data = new { 
                        isDuplicate = duplicate != null, 
                        existingTemplate = duplicate 
                    } 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for duplicate template");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        private Guid GetUserIdFromToken()
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader != null && authHeader.StartsWith("Bearer "))
            {
                var token = authHeader.Substring("Bearer ".Length).Trim();
                
                try
                {
                    var handler = new JwtSecurityTokenHandler();
                    var jsonToken = handler.ReadJwtToken(token);
                    
                    var userIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "sub" || x.Type == "userId" || x.Type == ClaimTypes.NameIdentifier);
                    
                    if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        return userId;
                    }
                }
                catch
                {
                    // Token parsing failed
                }
            }
            
            return Guid.Empty;
        }
    }

    // API Request DTOs for standardized request format
    public class CreateWlpTemplateRequestDto
    {
        public required CreateWlpTemplatePayloadDto Payload { get; set; }
    }

    public class CreateWlpTemplatePayloadDto
    {
        [Required]
        [StringLength(200, MinimumLength = 1)]
        public required string Name { get; set; }

        public string? Description { get; set; }

        [Required]
        [StringLength(255, MinimumLength = 1)]
        public required string FileName { get; set; }

        [Required]
        public required string FileContentBase64 { get; set; }
    }

    public class WlpTemplateListApiRequestDto
    {
        public required WlpTemplateListPayloadDto Payload { get; set; }
    }

    public class WlpTemplateListPayloadDto
    {
        public string? SearchTerm { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? PageOffset { get; set; }
        public int? PageLimit { get; set; }
    }

    public class GetWlpTemplateRequestDto
    {
        public required GetWlpTemplatePayloadDto Payload { get; set; }
    }

    public class GetWlpTemplatePayloadDto
    {
        [Required]
        public Guid TemplateId { get; set; }
    }

    public class UpdateWlpTemplateApiRequestDto
    {
        public required UpdateWlpTemplatePayloadDto Payload { get; set; }
    }

    public class UpdateWlpTemplatePayloadDto
    {
        [Required]
        public Guid Id { get; set; }

        [Required]
        [StringLength(200, MinimumLength = 1)]
        public required string Name { get; set; }

        public string? Description { get; set; }
    }

    public class DeleteWlpTemplateRequestDto
    {
        public required DeleteWlpTemplatePayloadDto Payload { get; set; }
    }

    public class DeleteWlpTemplatePayloadDto
    {
        [Required]
        public Guid TemplateId { get; set; }
    }

    public class GetWlpTemplatePolygonsRequestDto
    {
        public required GetWlpTemplatePolygonsPayloadDto Payload { get; set; }
    }

    public class GetWlpTemplatePolygonsPayloadDto
    {
        [Required]
        public Guid TemplateId { get; set; }
    }

    public class CheckDuplicateWlpTemplateRequestDto
    {
        public required CheckDuplicateWlpTemplatePayloadDto Payload { get; set; }
    }

    public class CheckDuplicateWlpTemplatePayloadDto
    {
        [Required]
        public required string FileContentBase64 { get; set; }
    }
}