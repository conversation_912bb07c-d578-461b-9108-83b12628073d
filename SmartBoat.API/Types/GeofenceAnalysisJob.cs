namespace SmartBoat.API.Types
{
    public class GeofenceAnalysisJob
    {
        public Guid Id { get; set; }
        public Guid VesselId { get; set; }
        public DateTime AnalysisDateFrom { get; set; }
        public DateTime AnalysisDateTo { get; set; }
        public string JobStatus { get; set; } = "Pending";
        public Guid CreatedBy { get; set; }
        
        // Job execution tracking
        public DateTime? StartedTimestamp { get; set; }
        public DateTime? CompletedTimestamp { get; set; }
        
        // Results summary
        public int CoordinatesProcessed { get; set; }
        public int FenceEventsCreated { get; set; }
        
        // Error handling
        public string? ErrorMessage { get; set; }
        
        // Audit fields
        public DateTime Created { get; set; }
        public DateTime? Changed { get; set; }
        public Guid? ChangedBy { get; set; }
    }

    public class GeofenceAnalysisJobDto
    {
        public Guid Id { get; set; }
        public Guid VesselId { get; set; }
        public string VesselName { get; set; } = string.Empty;
        public DateTime AnalysisDateFrom { get; set; }
        public DateTime AnalysisDateTo { get; set; }
        public string JobStatus { get; set; } = string.Empty;
        public List<string> TemplateNames { get; set; } = new List<string>();
        
        // Job execution tracking
        public DateTime? StartedTimestamp { get; set; }
        public DateTime? CompletedTimestamp { get; set; }
        public TimeSpan? Duration { get; set; }
        
        // Results summary
        public int CoordinatesProcessed { get; set; }
        public int FenceEventsCreated { get; set; }
        
        // Error handling
        public string? ErrorMessage { get; set; }
        
        public DateTime Created { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
    }

    public class CreateGeofenceAnalysisJobDto
    {
        public Guid VesselId { get; set; }
        public DateTime AnalysisDateFrom { get; set; }
        public DateTime AnalysisDateTo { get; set; }
        public List<Guid> TemplateIds { get; set; } = new List<Guid>();
    }

    public class GetGeofenceAnalysisJobsDto
    {
        public Guid? VesselId { get; set; }
        public string? JobStatus { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? PageOffset { get; set; }
        public int? PageLimit { get; set; }
        public string? SearchTerm { get; set; }
    }

    public class GeofenceAnalysisJobListRequestDto
    {
        public Guid VesselId { get; set; }
        public string? JobStatus { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? PageOffset { get; set; }
        public int? PageLimit { get; set; }
    }

    public enum GeofenceAnalysisJobStatus
    {
        Pending,
        Running,
        Completed,
        Failed
    }
}