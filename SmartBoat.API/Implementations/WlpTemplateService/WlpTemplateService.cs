using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using SmartBoat.API.Implementations.GeoFencingService;
using System.Security.Cryptography;
using Nbg.NetCore.DatabaseService;

namespace SmartBoat.API.Implementations.WlpTemplateService
{
    public class WlpTemplateService : IWlpTemplateService
    {
        private readonly IDatabaseService _databaseService;
        private readonly IWlpGeofenceParser _wlpParser;
        private readonly ILogger<WlpTemplateService> _logger;

        public WlpTemplateService(
            IDatabaseService databaseService,
            IWlpGeofenceParser wlpParser,
            ILogger<WlpTemplateService> logger)
        {
            _databaseService = databaseService;
            _wlpParser = wlpParser;
            _logger = logger;
        }

        public async Task<WlpTemplateDto> CreateTemplateAsync(CreateWlpTemplateDto request, Guid userId)
        {
            try
            {
                // Calculate file content hash for duplicate detection
                var contentHash = CalculateHash(request.FileContent);
                
                // Check for existing template with same hash
                var existingTemplate = await FindDuplicateTemplateAsync(contentHash, userId);
                if (existingTemplate != null)
                {
                    throw new InvalidOperationException($"A template with identical content already exists: {existingTemplate.Name}");
                }

                // Parse WLP file to extract polygon data
                var wlpData = await _wlpParser.ParseWlpContentAsync(request.FileContent);
                if (wlpData == null)
                {
                    throw new InvalidOperationException("Failed to parse WLP file");
                }
                
                var polygons = _wlpParser.ConvertToGeofencePolygons(wlpData);
                if (!polygons.Any())
                {
                    throw new InvalidOperationException("No valid polygons found in WLP file");
                }

                var templateId = Guid.NewGuid();

                // Create template record
                var template = new WlpTemplate
                {
                    Id = templateId,
                    Name = request.Name,
                    Description = request.Description,
                    FileName = request.FileName,
                    OriginalFilename = request.OriginalFilename,
                    FileSize = request.FileContent.Length,
                    FileContentHash = contentHash,
                    ZoneCount = polygons.Count(),
                    UploadedBy = userId,
                    UploadTimestamp = DateTime.UtcNow,
                    CreatedBy = userId,
                    Created = DateTime.UtcNow,
                    IsActive = true,
                    IsDeleted = false
                };

                await _databaseService.InsertAsync<WlpTemplate>(template);

                // Create polygon data records
                var polygonDataList = new List<WlpPolygonData>();
                foreach (var polygon in polygons)
                {
                    var polygonData = new WlpPolygonData
                    {
                        Id = Guid.NewGuid(),
                        TemplateId = templateId,
                        ZoneId = 0, // Will be assigned based on order
                        ZoneName = polygon.Name,
                        ZoneDescription = null,
                        PolygonData = System.Text.Json.JsonSerializer.Serialize(polygon.Points),
                        PointCount = polygon.Points?.Count ?? 0,
                        MinLat = (float)polygon.Points.Min(p => p.Latitude),
                        MaxLat = (float)polygon.Points.Max(p => p.Latitude),
                        MinLng = (float)polygon.Points.Min(p => p.Longitude),
                        MaxLng = (float)polygon.Points.Max(p => p.Longitude),
                        BoundingBoxMinLat = (decimal)polygon.Points.Min(p => p.Latitude),
                        BoundingBoxMaxLat = (decimal)polygon.Points.Max(p => p.Latitude),
                        BoundingBoxMinLng = (decimal)polygon.Points.Min(p => p.Longitude),
                        BoundingBoxMaxLng = (decimal)polygon.Points.Max(p => p.Longitude),
                        Created = DateTime.UtcNow,
                        CreatedBy = userId
                    };
                    
                    polygonDataList.Add(polygonData);
                    await _databaseService.InsertAsync<WlpPolygonData>(polygonData);
                }

                _logger.LogInformation("Created WLP template {TemplateId} with {ZoneCount} zones for user {UserId}", 
                    templateId, template.ZoneCount, userId);

                return await MapToDto(template, polygonDataList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating WLP template for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<WlpTemplateDto>> GetTemplatesAsync(WlpTemplateListRequestDto request, Guid userId)
        {
            try
            {
                // Use SelectAsync with filter object instead of custom SQL
                var templates = await _databaseService.SelectAsync<WlpTemplate>(new { 
                    CreatedBy = userId, 
                    IsDeleted = false 
                });

                var templateDtos = new List<WlpTemplateDto>();

                foreach (var template in templates ?? new List<WlpTemplate>())
                {
                    var polygons = await _databaseService.SelectAsync<WlpPolygonData>(new { TemplateId = template.Id });
                    templateDtos.Add(await MapToDto(template, polygons?.ToList() ?? new List<WlpPolygonData>()));
                }

                // Apply filtering and paging in memory for now
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    templateDtos = templateDtos.Where(t => 
                        t.Name.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        (t.Description != null && t.Description.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase))
                    ).ToList();
                }

                return templateDtos.OrderByDescending(t => t.Created).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting WLP templates for user {UserId}", userId);
                throw;
            }
        }

        public async Task<WlpTemplateDto?> GetTemplateByIdAsync(Guid templateId, Guid userId)
        {
            try
            {
                var templates = await _databaseService.SelectAsync<WlpTemplate>(new { 
                    Id = templateId, 
                    CreatedBy = userId, 
                    IsDeleted = false 
                });
                var template = templates?.FirstOrDefault();

                if (template == null)
                    return null;

                var polygons = await _databaseService.SelectAsync<WlpPolygonData>(new { TemplateId = templateId });

                return await MapToDto(template, polygons?.ToList() ?? new List<WlpPolygonData>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting WLP template {TemplateId} for user {UserId}", templateId, userId);
                throw;
            }
        }

        public async Task<WlpTemplateDto> UpdateTemplateAsync(UpdateWlpTemplateDto request, Guid userId)
        {
            try
            {
                var templates = await _databaseService.SelectAsync<WlpTemplate>(new { 
                    Id = request.Id, 
                    CreatedBy = userId, 
                    IsDeleted = false 
                });
                var template = templates?.FirstOrDefault();

                if (template == null)
                    throw new ArgumentException("Template not found or access denied");

                template.Name = request.Name;
                template.Description = request.Description;
                template.Changed = DateTime.UtcNow;
                template.ChangedBy = userId;

                await _databaseService.UpdateAsync<WlpTemplate>(template, new { Id = template.Id });

                var polygons = await _databaseService.SelectAsync<WlpPolygonData>(new { TemplateId = template.Id });

                _logger.LogInformation("Updated WLP template {TemplateId} for user {UserId}", template.Id, userId);

                return await MapToDto(template, polygons?.ToList() ?? new List<WlpPolygonData>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating WLP template {TemplateId} for user {UserId}", request.Id, userId);
                throw;
            }
        }

        public async Task<bool> DeleteTemplateAsync(Guid templateId, Guid userId)
        {
            try
            {
                var templates = await _databaseService.SelectAsync<WlpTemplate>(new { 
                    Id = templateId, 
                    CreatedBy = userId, 
                    IsDeleted = false 
                });
                var template = templates?.FirstOrDefault();

                if (template == null)
                    return false;

                // Note: For now we'll skip the active jobs check until those tables exist
                // TODO: Add active job check when GeofenceAnalysisJobs tables are created

                // Soft delete
                template.IsDeleted = true;
                template.Changed = DateTime.UtcNow;
                template.ChangedBy = userId;

                await _databaseService.UpdateAsync<WlpTemplate>(template, new { Id = templateId });

                _logger.LogInformation("Deleted WLP template {TemplateId} for user {UserId}", templateId, userId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting WLP template {TemplateId} for user {UserId}", templateId, userId);
                throw;
            }
        }

        public async Task<List<WlpPolygonDataDto>> GetTemplatePolygonsAsync(Guid templateId, Guid userId)
        {
            try
            {
                // Verify template access
                var templates = await _databaseService.SelectAsync<WlpTemplate>(new { 
                    Id = templateId, 
                    CreatedBy = userId, 
                    IsDeleted = false 
                });
                var template = templates?.FirstOrDefault();

                if (template == null)
                    throw new ArgumentException("Template not found or access denied");

                var polygons = await _databaseService.SelectAsync<WlpPolygonData>(new { TemplateId = templateId });

                return polygons?.OrderBy(p => p.ZoneName).Select(p => new WlpPolygonDataDto
                {
                    Id = p.Id,
                    TemplateId = p.TemplateId,
                    ZoneName = p.ZoneName,
                    PolygonData = p.PolygonData,
                    MinLat = p.MinLat,
                    MaxLat = p.MaxLat,
                    MinLng = p.MinLng,
                    MaxLng = p.MaxLng
                }).ToList() ?? new List<WlpPolygonDataDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting polygons for template {TemplateId} for user {UserId}", templateId, userId);
                throw;
            }
        }

        public async Task<WlpTemplateDto?> FindDuplicateTemplateAsync(string fileContentHash, Guid userId)
        {
            try
            {
                var templates = await _databaseService.SelectAsync<WlpTemplate>(new { 
                    FileContentHash = fileContentHash, 
                    CreatedBy = userId, 
                    IsDeleted = false 
                });
                var template = templates?.FirstOrDefault();

                if (template == null)
                    return null;

                var polygons = await _databaseService.SelectAsync<WlpPolygonData>(new { TemplateId = template.Id });

                return await MapToDto(template, polygons?.ToList() ?? new List<WlpPolygonData>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding duplicate template for user {UserId}", userId);
                throw;
            }
        }

        private async Task<WlpTemplateDto> MapToDto(WlpTemplate template, List<WlpPolygonData> polygons)
        {
            // For now, use a simple username lookup - this could be optimized with joins later
            // For now, skip the username lookup to avoid SelectAsync signature issues
            // TODO: Implement proper user lookup when needed
            // var users = await _databaseService.SelectAsync<dynamic>(new { Id = template.CreatedBy }, "Users");
            // var createdByUser = users?.FirstOrDefault();
            return new WlpTemplateDto
            {
                Id = template.Id,
                Name = template.Name,
                Description = template.Description,
                FileName = template.FileName,
                OriginalFilename = template.OriginalFilename,
                UploadTimestamp = template.UploadTimestamp,
                FileSize = template.FileSize,
                ZoneCount = template.ZoneCount,
                ZoneNames = polygons.Select(p => p.ZoneName).OrderBy(name => name).ToList(),
                Created = template.Created,
                CreatedByName = "Unknown", // TODO: Implement user lookup
                UploaderName = "Unknown" // TODO: Implement user lookup
            };
        }

        private string CalculateHash(byte[] content)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(content);
                return Convert.ToHexString(hashBytes);
            }
        }
    }
}