using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using CsvHelper;
using Microsoft.Extensions.Logging;

namespace SmartBoat.API.Services
{
    public partial class CsvProcessingService
    {
        public async Task<Response<List<Guid>>> ProcessCsvToSensorDataPointsAsync(byte[] csvContent, string fileName, string sourceEmail, Guid userId)
        {
            try
            {
                var createdDataPointIds = new List<Guid>();
                var vesselNameFromFile = ExtractVesselNameFromFileName(fileName);

                Console.WriteLine($"         🚢 [CSV TO SENSORS] Processing {fileName} for SensorDataPoints with userId: {userId}");
                _logger.LogInformation("Processing CSV file {FileName} to SensorDataPoints with userId: {UserId}", fileName, userId);

                using var stream = new MemoryStream(csvContent);
                using var reader = new StreamReader(stream, Encoding.UTF8);
                using var csv = new CsvReader(reader, GetCsvConfiguration());

                var records = new List<dynamic>();
                string[]? headers = null;

                if (csv.Read())
                {
                    csv.ReadHeader();
                    headers = csv.HeaderRecord;

                    Console.WriteLine($"         📋 [CSV TO SENSORS] Headers: {string.Join(", ", headers ?? new string[0])}");

                    while (csv.Read())
                    {
                        var record = csv.GetRecord<dynamic>();
                        if (record != null)
                        {
                            records.Add(record);
                        }
                    }
                }

                if (records.Count == 0)
                {
                    Console.WriteLine($"         ⚠️  [CSV TO SENSORS] No records found in CSV");
                    return new Response<List<Guid>>
                    {
                        Payload = createdDataPointIds,
                        Exception = null
                    };
                }

                // Group measurements by sensor and batch process them
                var sensorMeasurementBatches = new Dictionary<Guid, List<CreateSensorDataPointDto>>();
                var vesselPathPoints = new List<VesselPathPoint>();
                var processedRecordCount = 0;
                var vesselName = "";
                Guid? vesselId = null;

                foreach (var record in records)
                {
                    try
                    {
                        var recordDict = record as IDictionary<string, object>;
                        if (recordDict == null) continue;

                        // Extract vessel name from record or use filename fallback
                        vesselName = ExtractVesselNameFromRecord(recordDict) ?? vesselNameFromFile ?? "Unknown";
                        if (string.IsNullOrEmpty(vesselName))
                        {
                            Console.WriteLine($"         ⚠️  [CSV TO SENSORS] Skipping record - no vessel name found");
                            continue;
                        }

                        // Map CSV columns to measurements
                        var mappingResult = await _csvColumnMapper.MapCsvRecordAsync(recordDict, vesselName);

                        if (mappingResult.Measurements.Count == 0)
                        {
                            Console.WriteLine($"         ⚠️  [CSV TO SENSORS] No mappable measurements found in record");
                            continue;
                        }

                        // Get or create vessel sensor configuration
                        var vesselConfig = await _vesselSensorManager.GetOrCreateVesselSensorConfigurationAsync(
                            vesselName, mappingResult.VesselEngineConfiguration);

                        // Extract vessel ID for path point creation (get it from the config)
                        if (!vesselId.HasValue)
                        {
                            vesselId = vesselConfig.VesselId;
                        }

                        // Extract coordinates for vessel path tracking
                        var recordTimestamp = ExtractTimestamp(mappingResult.Measurements) ?? DateTime.UtcNow;
                        var coordinatesExtracted = ExtractCoordinatesFromRecord(recordDict, recordTimestamp, vesselId.Value);
                        if (coordinatesExtracted != null)
                        {
                            vesselPathPoints.Add(coordinatesExtracted);
                            
                            // Check geo-fencing for this coordinate
                            try
                            {
                                await _geoFencingService.CheckVesselPositionAsync(
                                    vesselId.Value, 
                                    coordinatesExtracted.Lat.Value, 
                                    coordinatesExtracted.Lng.Value, 
                                    recordTimestamp);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Failed to check geo-fencing for vessel {VesselId} at coordinates {Lat},{Lng}", 
                                    vesselId.Value, coordinatesExtracted.Lat, coordinatesExtracted.Lng);
                            }
                        }

                        // Group measurements by sensor category and create data points
                        var measurementsByCategory = GroupMeasurementsByCategory(mappingResult.Measurements);

                        foreach (var categoryGroup in measurementsByCategory)
                        {
                            var sensorId = await _vesselSensorManager.GetSensorIdForMeasurementAsync(
                                vesselName, categoryGroup.Key, mappingResult.VesselEngineConfiguration);

                            var dataPointDto = new CreateSensorDataPointDto
                            {
                                SensorId = sensorId,
                                Timestamp = recordTimestamp,
                                Measurements = categoryGroup.Value,
                                QualityScore = mappingResult.QualityScore,
                                Source = "EMAIL_IMPORT"
                            };

                            if (!sensorMeasurementBatches.ContainsKey(sensorId))
                            {
                                sensorMeasurementBatches[sensorId] = new List<CreateSensorDataPointDto>();
                            }
                            sensorMeasurementBatches[sensorId].Add(dataPointDto);
                        }

                        processedRecordCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error processing individual record in CSV file {FileName}", fileName);
                        continue;
                    }
                }

                // Bulk create sensor data points for each sensor
                foreach (var batch in sensorMeasurementBatches)
                {
                    var sensorId = batch.Key;
                    var dataPoints = batch.Value;

                    Console.WriteLine($"         💾 [CSV TO SENSORS] Creating {dataPoints.Count} data points for sensor {sensorId}");

                    foreach (var dataPoint in dataPoints)
                    {
                        try
                        {
                            var dataPointIdString = await _sensorDataPointService.Create(dataPoint, userId);
                            if (Guid.TryParse(dataPointIdString, out var dataPointId))
                            {
                                createdDataPointIds.Add(dataPointId);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to create sensor data point for sensor {SensorId}", sensorId);
                            continue;
                        }
                    }
                }

                // Create vessel path points from extracted coordinates
                if (vesselPathPoints.Any())
                {
                    try
                    {
                        Console.WriteLine($"         🗺️  [CSV TO PATH] Creating {vesselPathPoints.Count} vessel path points for vessel {vesselName}");
                        var pathPointIds = await _vesselPathPointService.CreatePathPointsBulkAsync(vesselPathPoints, userId);
                        Console.WriteLine($"         ✅ [CSV TO PATH] Created {pathPointIds.Count} vessel path points from {vesselPathPoints.Count} coordinate records");
                        _logger.LogInformation("Successfully created {PathPointCount} vessel path points from {CoordinateCount} CSV coordinate records for vessel {VesselName}",
                            pathPointIds.Count, vesselPathPoints.Count, vesselName);
                    }
                    catch (Exception ex)
                    {
                        // Don't fail the whole operation if path point creation fails
                        Console.WriteLine($"         ⚠️  [CSV TO PATH] Failed to create vessel path points: {ex.Message}");
                        _logger.LogWarning(ex, "Failed to create vessel path points for vessel {VesselName}, but sensor data processing succeeded", vesselName);
                    }
                }
                else
                {
                    Console.WriteLine($"         ℹ️  [CSV TO PATH] No valid coordinates found for vessel path tracking");
                    _logger.LogInformation("No valid coordinates found in CSV for vessel path tracking for vessel {VesselName}", vesselName);
                }

                Console.WriteLine($"         ✅ [CSV TO SENSORS] Created {createdDataPointIds.Count} sensor data points from {processedRecordCount} CSV records for vessel {vesselName}");
                _logger.LogInformation("Successfully created {DataPointCount} sensor data points from {RecordCount} CSV records for vessel {VesselName}",
                    createdDataPointIds.Count, processedRecordCount, vesselName);

                return new Response<List<Guid>>
                {
                    Payload = createdDataPointIds,
                    Exception = null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing CSV file {FileName} to SensorDataPoints", fileName);
                return new Response<List<Guid>>
                {
                    Payload = null,
                    Exception = new ResponseException
                    {
                        Id = Guid.NewGuid(),
                        Code = "CSV-500",
                        Description = "Error processing CSV file to sensor data points",
                        Category = "File Processing Error"
                    }
                };
            }
        }

        private string? ExtractVesselNameFromRecord(IDictionary<string, object> recordDict)
        {
            var possibleVesselKeys = new[] { "vessel", "vesselname", "vessel_name", "boat", "boatname" };

            foreach (var key in possibleVesselKeys)
            {
                var matchingKey = recordDict.Keys.FirstOrDefault(k =>
                    string.Equals(k, key, StringComparison.OrdinalIgnoreCase));

                if (matchingKey != null)
                {
                    var value = recordDict[matchingKey]?.ToString()?.Trim();
                    if (!string.IsNullOrEmpty(value))
                    {
                        return value;
                    }
                }
            }

            return null;
        }

        private Dictionary<string, Dictionary<string, object>> GroupMeasurementsByCategory(Dictionary<string, object> measurements)
        {
            var grouped = new Dictionary<string, Dictionary<string, object>>();

            foreach (var measurement in measurements)
            {
                var category = DetermineMeasurementCategory(measurement.Key);

                if (!grouped.ContainsKey(category))
                {
                    grouped[category] = new Dictionary<string, object>();
                }

                grouped[category][measurement.Key] = measurement.Value;
            }

            return grouped;
        }

        private string DetermineMeasurementCategory(string measurementKey)
        {
            var key = measurementKey.ToLowerInvariant();

            if (key.Contains("port") && (key.Contains("engine") || key.Contains("rpm")))
                return "engine_port";

            if ((key.Contains("stbd") || key.Contains("starboard")) && (key.Contains("engine") || key.Contains("rpm")))
                return "engine_stbd";

            if (key.Contains("engine") || key.Contains("rpm") || key.Contains("fuel") || key.Contains("oil"))
                return "engine_general";

            if (key.Contains("wind") || key.Contains("water") || key.Contains("sea") || key.Contains("depth"))
                return "environmental";

            if (key.Contains("speed") || key.Contains("course") || key.Contains("coordinates") || key.Contains("position"))
                return "navigation";

            return "systems";
        }

        private DateTime? ExtractTimestamp(Dictionary<string, object> measurements)
        {
            var timestampKeys = new[] { "timestamp", "datetime", "sensortime", "sensor_time", "time" };

            foreach (var key in timestampKeys)
            {
                if (measurements.TryGetValue(key, out var value))
                {
                    if (value is DateTime dateTime)
                        return dateTime;

                    if (DateTime.TryParse(value?.ToString(), out var parsedDateTime))
                        return parsedDateTime;
                }
            }

            return null;
        }

        private VesselPathPoint? ExtractCoordinatesFromRecord(IDictionary<string, object> recordDict, DateTime timestamp, Guid vesselId)
        {
            try
            {
                // Look for coordinate columns in various common formats
                var coordinateKeys = new[] { "coordinates", "groupcoordinates", "group_coordinates", "position", "location_coordinates", "gps_coordinates", "lat_lng" };

                foreach (var key in coordinateKeys)
                {
                    var matchingKey = recordDict.Keys.FirstOrDefault(k =>
                        string.Equals(k, key, StringComparison.OrdinalIgnoreCase));

                    if (matchingKey != null)
                    {
                        var coordinateValue = recordDict[matchingKey]?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(coordinateValue))
                        {
                            var parsedCoordinates = _vesselPathPointService.ParseCoordinates(coordinateValue);
                            if (parsedCoordinates.HasValue)
                            {
                                return new VesselPathPoint
                                {
                                    Id = Guid.NewGuid(),
                                    VesselId = vesselId,
                                    Lat = parsedCoordinates.Value.lat,
                                    Lng = parsedCoordinates.Value.lng,
                                    Timestamp = timestamp,
                                    Location = coordinateValue, // Store original coordinate string for reference
                                    Created = DateTime.UtcNow,
                                    Changed = DateTime.UtcNow
                                };
                            }
                        }
                    }
                }

                // Also try to extract separate latitude and longitude columns
                var latKeys = new[] { "latitude", "lat", "lat_decimal", "latitude_deg" };
                var lngKeys = new[] { "longitude", "lng", "lon", "lng_decimal", "longitude_deg" };

                float? latitude = null;
                float? longitude = null;

                foreach (var latKey in latKeys)
                {
                    var matchingLatKey = recordDict.Keys.FirstOrDefault(k =>
                        string.Equals(k, latKey, StringComparison.OrdinalIgnoreCase));
                    if (matchingLatKey != null)
                    {
                        var latValue = recordDict[matchingLatKey]?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(latValue) && float.TryParse(latValue, out var lat))
                        {
                            latitude = lat;
                            break;
                        }
                    }
                }

                foreach (var lngKey in lngKeys)
                {
                    var matchingLngKey = recordDict.Keys.FirstOrDefault(k =>
                        string.Equals(k, lngKey, StringComparison.OrdinalIgnoreCase));
                    if (matchingLngKey != null)
                    {
                        var lngValue = recordDict[matchingLngKey]?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(lngValue) && float.TryParse(lngValue, out var lng))
                        {
                            longitude = lng;
                            break;
                        }
                    }
                }

                if (latitude.HasValue && longitude.HasValue && _vesselPathPointService.ValidateCoordinates(latitude, longitude))
                {
                    return new VesselPathPoint
                    {
                        Id = Guid.NewGuid(),
                        VesselId = vesselId,
                        Lat = latitude,
                        Lng = longitude,
                        Timestamp = timestamp,
                        Location = $"{latitude},{longitude}",
                        Created = DateTime.UtcNow,
                        Changed = DateTime.UtcNow
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error extracting coordinates from CSV record for vessel {VesselId}", vesselId);
                return null;
            }
        }
    }
}