using SmartBoat.API.Interfaces;
using SmartBoat.API.Types;
using SmartBoat.API.Implementations.GeoFencingService;
using Nbg.NetCore.DatabaseService;
using System.Text.Json;

namespace SmartBoat.API.Implementations.GeofenceAnalysisService
{
    public partial class GeofenceAnalysisService : IGeofenceAnalysisService
    {
        private readonly IDatabaseService _databaseService;
        private readonly IWlpGeofenceParser _wlpParser;
        private readonly ILogger<GeofenceAnalysisService> _logger;

        public GeofenceAnalysisService(
            IDatabaseService databaseService,
            IWlpGeofenceParser wlpParser,
            ILogger<GeofenceAnalysisService> logger)
        {
            _databaseService = databaseService;
            _wlpParser = wlpParser;
            _logger = logger;
        }

        public async Task<GeofenceAnalysisJobDto> CreateAnalysisJobAsync(CreateGeofenceAnalysisJobDto request, Guid userId)
        {
            try
            {
                _logger.LogInformation("Creating geofence analysis job for user {UserId}, vessel {VesselId}", userId, request.VesselId);

                // Validate vessel exists and user has access
                var vessels = await _databaseService.SelectAsync<dynamic>(new { 
                    Id = request.VesselId,
                    // Add ownership/access validation here if needed
                });
                var vessel = vessels?.FirstOrDefault();
                if (vessel == null)
                {
                    throw new ArgumentException("Vessel not found or access denied");
                }

                // Validate templates exist and user has access
                var templateIds = request.TemplateIds.ToList();
                var templates = new List<WlpTemplate>();
                
                foreach (var templateId in templateIds)
                {
                    var templateResults = await _databaseService.SelectAsync<WlpTemplate>(new { 
                        Id = templateId, 
                        CreatedBy = userId, 
                        IsDeleted = false 
                    });
                    var template = templateResults?.FirstOrDefault();
                    if (template == null)
                    {
                        throw new ArgumentException($"Template {templateId} not found or access denied");
                    }
                    templates.Add(template);
                }

                if (!templates.Any())
                {
                    throw new ArgumentException("No valid templates found");
                }

                // Create the analysis job
                var jobId = Guid.NewGuid();
                var analysisJob = new GeofenceAnalysisJob
                {
                    Id = jobId,
                    VesselId = request.VesselId,
                    AnalysisDateFrom = request.AnalysisDateFrom,
                    AnalysisDateTo = request.AnalysisDateTo,
                    JobStatus = "Pending",
                    CoordinatesProcessed = 0,
                    FenceEventsCreated = 0,
                    CreatedBy = userId,
                    Created = DateTime.UtcNow
                };

                await _databaseService.InsertAsync<GeofenceAnalysisJob>(analysisJob);

                // Create job-template relationships
                foreach (var template in templates)
                {
                    var jobTemplate = new GeofenceJobTemplate
                    {
                        Id = Guid.NewGuid(),
                        JobId = jobId,
                        TemplateId = template.Id,
                        Created = DateTime.UtcNow,
                        CreatedBy = userId
                    };
                    await _databaseService.InsertAsync<GeofenceJobTemplate>(jobTemplate);
                }

                _logger.LogInformation("Created analysis job {JobId} with {TemplateCount} templates", jobId, templates.Count);

                // Start processing in background
                _ = Task.Run(() => ProcessAnalysisJobAsync(jobId, userId));

                return await MapToJobDto(analysisJob, templates.Select(t => t.Name).ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating geofence analysis job for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<GeofenceAnalysisJobDto>> GetAnalysisJobsAsync(GetGeofenceAnalysisJobsDto request, Guid userId)
        {
            try
            {
                // Get jobs for user
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new { CreatedBy = userId });
                var jobDtos = new List<GeofenceAnalysisJobDto>();

                foreach (var job in jobs ?? new List<GeofenceAnalysisJob>())
                {
                    // Get vessel name
                    var vessels = await _databaseService.SelectAsync<dynamic>(new { Id = job.VesselId });
                    var vessel = vessels?.FirstOrDefault();
                    var vesselName = vessel?.Name?.ToString() ?? "Unknown Vessel";

                    // Get template names for this job
                    var jobTemplates = await _databaseService.SelectAsync<GeofenceJobTemplate>(new { JobId = job.Id });
                    var templateNames = new List<string>();

                    if (jobTemplates?.Any() == true)
                    {
                        foreach (var jobTemplate in jobTemplates)
                        {
                            var templates = await _databaseService.SelectAsync<WlpTemplate>(new { Id = jobTemplate.TemplateId });
                            var template = templates?.FirstOrDefault();
                            if (template != null)
                            {
                                templateNames.Add(template.Name);
                            }
                        }
                    }

                    var jobDto = await MapToJobDto(job, templateNames, vesselName);
                    jobDtos.Add(jobDto);
                }

                return jobDtos.OrderByDescending(j => j.Created).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting analysis jobs for user {UserId}", userId);
                throw;
            }
        }

        public async Task<GeofenceAnalysisJobDto?> GetAnalysisJobByIdAsync(Guid jobId, Guid userId)
        {
            try
            {
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new { 
                    Id = jobId, 
                    CreatedBy = userId 
                });
                var job = jobs?.FirstOrDefault();

                if (job == null)
                    return null;

                // Get vessel name
                var vessels = await _databaseService.SelectAsync<dynamic>(new { Id = job.VesselId });
                var vessel = vessels?.FirstOrDefault();
                var vesselName = vessel?.Name?.ToString() ?? "Unknown Vessel";

                // Get template names for this job
                var jobTemplates = await _databaseService.SelectAsync<GeofenceJobTemplate>(new { JobId = job.Id });
                var templateNames = new List<string>();

                if (jobTemplates?.Any() == true)
                {
                    foreach (var jobTemplate in jobTemplates)
                    {
                        var templates = await _databaseService.SelectAsync<WlpTemplate>(new { Id = jobTemplate.TemplateId });
                        var template = templates?.FirstOrDefault();
                        if (template != null)
                        {
                            templateNames.Add(template.Name);
                        }
                    }
                }

                return await MapToJobDto(job, templateNames, vesselName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting analysis job {JobId} for user {UserId}", jobId, userId);
                throw;
            }
        }

        public async Task<bool> CancelAnalysisJobAsync(Guid jobId, Guid userId)
        {
            try
            {
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new { 
                    Id = jobId, 
                    CreatedBy = userId 
                });
                var job = jobs?.FirstOrDefault();

                if (job == null)
                    return false;

                // Can only cancel pending or running jobs
                if (job.JobStatus != "Pending" && job.JobStatus != "Running")
                    return false;

                job.JobStatus = "Cancelled";
                job.Changed = DateTime.UtcNow;
                job.ChangedBy = userId;

                await _databaseService.UpdateAsync<GeofenceAnalysisJob>(job, new { Id = jobId });

                _logger.LogInformation("Cancelled analysis job {JobId} for user {UserId}", jobId, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling analysis job {JobId} for user {UserId}", jobId, userId);
                throw;
            }
        }

        public async Task<bool> DeleteAnalysisJobAsync(Guid jobId, Guid userId)
        {
            try
            {
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new { 
                    Id = jobId, 
                    CreatedBy = userId 
                });
                var job = jobs?.FirstOrDefault();

                if (job == null)
                    return false;

                // Delete job-template relationships first
                var jobTemplates = await _databaseService.SelectAsync<GeofenceJobTemplate>(new { JobId = jobId });
                if (jobTemplates?.Any() == true)
                {
                    foreach (var jobTemplate in jobTemplates)
                    {
                        await _databaseService.DeleteAsync<GeofenceJobTemplate>(new { Id = jobTemplate.Id });
                    }
                }

                // Delete any fence events created by this job
                // TODO: Implement when GeofenceEvents table is created

                // Delete the job
                await _databaseService.DeleteAsync<GeofenceAnalysisJob>(new { Id = jobId });

                _logger.LogInformation("Deleted analysis job {JobId} for user {UserId}", jobId, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting analysis job {JobId} for user {UserId}", jobId, userId);
                throw;
            }
        }

        public async Task<List<GeofenceAnalysisJobDto>> FindExistingAnalysisAsync(Guid vesselId, List<Guid> templateIds, DateTime dateFrom, DateTime dateTo, Guid userId)
        {
            try
            {
                // This would find existing analysis jobs with similar parameters
                // For now, return empty list
                return new List<GeofenceAnalysisJobDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding existing analysis for user {UserId}", userId);
                throw;
            }
        }

        public async Task ProcessCoordinatesAsync(Guid jobId, Guid vesselId, List<Guid> templateIds, DateTime dateFrom, DateTime dateTo, Guid userId)
        {
            // This is called by the background processing - delegate to the private method
            await ProcessAnalysisJobAsync(jobId, userId);
        }


        private async Task ProcessAnalysisJobAsync(Guid jobId, Guid userId)
        {
            try
            {
                _logger.LogInformation("Starting processing of analysis job {JobId}", jobId);

                // Update job status to running
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new { Id = jobId });
                var job = jobs?.FirstOrDefault();
                if (job == null)
                {
                    _logger.LogWarning("Analysis job {JobId} not found for processing", jobId);
                    return;
                }

                job.JobStatus = "Running";
                job.StartedTimestamp = DateTime.UtcNow;
                await _databaseService.UpdateAsync<GeofenceAnalysisJob>(job, new { Id = jobId });

                // Get job templates and load polygon data
                var jobTemplates = await _databaseService.SelectAsync<GeofenceJobTemplate>(new { JobId = jobId });
                if (jobTemplates?.Any() != true)
                {
                    await MarkJobAsFailed(jobId, "No templates found for analysis job");
                    return;
                }

                var geofencePolygons = new List<GeofencePolygonWithMetadata>();
                
                foreach (var jobTemplate in jobTemplates)
                {
                    // Get template metadata
                    var templates = await _databaseService.SelectAsync<WlpTemplate>(new { Id = jobTemplate.TemplateId });
                    var template = templates?.FirstOrDefault();
                    if (template == null) continue;

                    var polygons = await _databaseService.SelectAsync<WlpPolygonData>(new { TemplateId = jobTemplate.TemplateId });
                    if (polygons?.Any() == true)
                    {
                        foreach (var polygon in polygons)
                        {
                            // Deserialize polygon data
                            var points = JsonSerializer.Deserialize<List<GeofencePoint>>(polygon.PolygonData);
                            if (points?.Any() == true)
                            {
                                geofencePolygons.Add(new GeofencePolygonWithMetadata
                                {
                                    Id = polygon.Id,
                                    Name = polygon.ZoneName,
                                    Points = points,
                                    TemplateId = jobTemplate.TemplateId,
                                    TemplateName = template.Name
                                });
                            }
                        }
                    }
                }

                if (!geofencePolygons.Any())
                {
                    await MarkJobAsFailed(jobId, "No valid polygons found for analysis");
                    return;
                }

                // Get vessel coordinates for the date range
                var coordinates = await GetVesselCoordinatesAsync(job.VesselId, job.AnalysisDateFrom, job.AnalysisDateTo);
                if (!coordinates.Any())
                {
                    await MarkJobAsCompleted(jobId, 0, 0);
                    return;
                }

                _logger.LogInformation("Processing {CoordinateCount} coordinates against {PolygonCount} polygons for job {JobId}", 
                    coordinates.Count, geofencePolygons.Count, jobId);

                // Process coordinates with fence event tracking
                var eventsCreated = await ProcessCoordinatesWithFenceEventsAsync(jobId, coordinates, geofencePolygons);
                var coordinatesProcessed = coordinates.Count;

                // Mark job as completed
                await MarkJobAsCompleted(jobId, coordinatesProcessed, eventsCreated);

                _logger.LogInformation("Completed processing of analysis job {JobId}: {CoordinatesProcessed} coordinates, {EventsCreated} events", 
                    jobId, coordinatesProcessed, eventsCreated);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing analysis job {JobId}", jobId);
                await MarkJobAsFailed(jobId, ex.Message);
            }
        }

        private async Task<List<VesselCoordinate>> GetVesselCoordinatesAsync(Guid vesselId, DateTime dateFrom, DateTime dateTo)
        {
            try
            {
                // This would typically query the VesselPathPoints or similar table
                // For now, using a simplified approach
                var coordinates = new List<VesselCoordinate>();

                // Query vessel path points - this is a placeholder implementation
                // In a real system, you'd have a table with vessel coordinate history
                // var pathPoints = await _databaseService.SelectAsync<VesselPathPoint>(new { 
                //     VesselId = vesselId,
                //     // Date range filter would go here
                // });

                // For now, return empty list - this would be populated from actual vessel tracking data
                return coordinates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vessel coordinates for vessel {VesselId}", vesselId);
                throw;
            }
        }

        private bool IsPointInPolygon(VesselCoordinate coordinate, GeofencePolygonWithMetadata polygon)
        {
            // Ray casting algorithm for point-in-polygon test
            var point = new GeofencePoint { Latitude = coordinate.Latitude, Longitude = coordinate.Longitude };
            var vertices = polygon.Points;
            
            if (vertices.Count < 3)
                return false;

            bool inside = false;
            int j = vertices.Count - 1;

            for (int i = 0; i < vertices.Count; i++)
            {
                double xi = vertices[i].Latitude;
                double yi = vertices[i].Longitude;
                double xj = vertices[j].Latitude;
                double yj = vertices[j].Longitude;

                if (((yi > point.Longitude) != (yj > point.Longitude)) &&
                    (point.Latitude < (xj - xi) * (point.Longitude - yi) / (yj - yi) + xi))
                {
                    inside = !inside;
                }
                j = i;
            }

            return inside;
        }

        private async Task UpdateJobProgress(Guid jobId, int coordinatesProcessed, int eventsCreated)
        {
            try
            {
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new { Id = jobId });
                var job = jobs?.FirstOrDefault();
                if (job != null)
                {
                    job.CoordinatesProcessed = coordinatesProcessed;
                    job.FenceEventsCreated = eventsCreated;
                    await _databaseService.UpdateAsync<GeofenceAnalysisJob>(job, new { Id = jobId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating job progress for job {JobId}", jobId);
            }
        }

        private async Task MarkJobAsCompleted(Guid jobId, int coordinatesProcessed, int eventsCreated)
        {
            try
            {
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new { Id = jobId });
                var job = jobs?.FirstOrDefault();
                if (job != null)
                {
                    job.JobStatus = "Completed";
                    job.CompletedTimestamp = DateTime.UtcNow;
                    job.CoordinatesProcessed = coordinatesProcessed;
                    job.FenceEventsCreated = eventsCreated;
                    await _databaseService.UpdateAsync<GeofenceAnalysisJob>(job, new { Id = jobId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking job {JobId} as completed", jobId);
            }
        }

        private async Task MarkJobAsFailed(Guid jobId, string errorMessage)
        {
            try
            {
                var jobs = await _databaseService.SelectAsync<GeofenceAnalysisJob>(new { Id = jobId });
                var job = jobs?.FirstOrDefault();
                if (job != null)
                {
                    job.JobStatus = "Failed";
                    job.CompletedTimestamp = DateTime.UtcNow;
                    job.ErrorMessage = errorMessage;
                    await _databaseService.UpdateAsync<GeofenceAnalysisJob>(job, new { Id = jobId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking job {JobId} as failed", jobId);
            }
        }

        private async Task<GeofenceAnalysisJobDto> MapToJobDto(GeofenceAnalysisJob job, List<string> templateNames, string? vesselName = null)
        {
            return new GeofenceAnalysisJobDto
            {
                Id = job.Id,
                VesselId = job.VesselId,
                VesselName = vesselName ?? "Unknown Vessel",
                TemplateNames = templateNames,
                AnalysisDateFrom = job.AnalysisDateFrom,
                AnalysisDateTo = job.AnalysisDateTo,
                JobStatus = job.JobStatus,
                StartedTimestamp = job.StartedTimestamp,
                CompletedTimestamp = job.CompletedTimestamp,
                CoordinatesProcessed = job.CoordinatesProcessed,
                FenceEventsCreated = job.FenceEventsCreated,
                ErrorMessage = job.ErrorMessage,
                Created = job.Created
            };
        }
    }

    // Helper classes
    public class VesselCoordinate
    {
        public Guid VesselId { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class GeofencePolygon
    {
        public string Name { get; set; } = string.Empty;
        public List<GeofencePoint> Points { get; set; } = new();
        public Guid TemplateId { get; set; }
    }
}