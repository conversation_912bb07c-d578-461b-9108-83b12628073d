{"version": 2, "dgSpecHash": "J0rZRQ7jvBQ=", "success": true, "projectFilePath": "/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/SmartBoat.API.Tests.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/azure.core/1.39.0/azure.core.1.39.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/azure.identity/1.11.4/azure.identity.1.11.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/bcrypt.net-next/4.0.3/bcrypt.net-next.4.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/castle.core/5.1.1/castle.core.5.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/csvhelper/33.0.1/csvhelper.33.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/dapper.strongname/2.1.66/dapper.strongname.2.1.66.nupkg.sha512", "/Users/<USER>/.nuget/packages/htmlagilitypack/1.12.1/htmlagilitypack.1.12.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.jwtbearer/8.0.0/microsoft.aspnetcore.authentication.jwtbearer.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.openapi/8.0.0/microsoft.aspnetcore.openapi.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/1.1.1/microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.cryptography/8.0.0/microsoft.bcl.cryptography.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codecoverage/17.6.0/microsoft.codecoverage.17.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.data.sqlclient/6.0.2/microsoft.data.sqlclient.6.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.data.sqlclient.sni.runtime/6.0.2/microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/6.0.5/microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/8.0.0/microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/8.0.1/microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.5/microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/9.0.5/microsoft.extensions.configuration.binder.9.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/8.0.0/microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.5/microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/8.0.0/microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/8.0.0/microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/8.0.0/microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/8.0.0/microsoft.extensions.logging.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.2/microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/9.0.5/microsoft.extensions.options.9.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/9.0.5/microsoft.extensions.options.configurationextensions.9.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.5/microsoft.extensions.primitives.9.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.graph/5.56.0/microsoft.graph.5.56.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.graph.auth/1.0.0-preview.7/microsoft.graph.auth.1.0.0-preview.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.graph.core/3.1.12/microsoft.graph.core.3.1.12.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identity.client/4.61.3/microsoft.identity.client.4.61.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identity.client.extensions.msal/4.61.3/microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/7.6.0/microsoft.identitymodel.abstractions.7.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.jsonwebtokens/7.6.0/microsoft.identitymodel.jsonwebtokens.7.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.logging/7.6.0/microsoft.identitymodel.logging.7.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.protocols/7.6.0/microsoft.identitymodel.protocols.7.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.protocols.openidconnect/7.6.0/microsoft.identitymodel.protocols.openidconnect.7.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.tokens/7.6.0/microsoft.identitymodel.tokens.7.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.kiota.abstractions/1.9.1/microsoft.kiota.abstractions.1.9.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.kiota.authentication.azure/1.1.7/microsoft.kiota.authentication.azure.1.1.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.kiota.http.httpclientlibrary/1.4.3/microsoft.kiota.http.httpclientlibrary.1.4.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.kiota.serialization.form/1.2.4/microsoft.kiota.serialization.form.1.2.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.kiota.serialization.json/1.3.3/microsoft.kiota.serialization.json.1.3.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.kiota.serialization.multipart/1.1.5/microsoft.kiota.serialization.multipart.1.1.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.kiota.serialization.text/1.2.2/microsoft.kiota.serialization.text.1.2.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.test.sdk/17.6.0/microsoft.net.test.sdk.17.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/1.1.0/microsoft.netcore.platforms.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.targets/1.1.0/microsoft.netcore.targets.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.openapi/1.4.3/microsoft.openapi.1.4.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.sqlserver.server/1.0.0/microsoft.sqlserver.server.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.6.0/microsoft.testplatform.objectmodel.17.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.6.0/microsoft.testplatform.testhost.17.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.primitives/4.3.0/microsoft.win32.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/moq/4.20.69/moq.4.20.69.nupkg.sha512", "/Users/<USER>/.nuget/packages/nbg.aspnetcore.dapper/*******/nbg.aspnetcore.dapper.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/nbg.netcore.autocodedboperations/1.1.0-ci-20250409-144812/nbg.netcore.autocodedboperations.1.1.0-ci-20250409-144812.nupkg.sha512", "/Users/<USER>/.nuget/packages/nbg.netcore.configuration.extensions/9.0.5/nbg.netcore.configuration.extensions.9.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/nbg.netcore.data/*******/nbg.netcore.data.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/nbg.netcore.databaseservice/8.1.5/nbg.netcore.databaseservice.8.1.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/nbg.netcore.utilities/8.0.4/nbg.netcore.utilities.8.0.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/netstandard.library/1.6.1/netstandard.library.1.6.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/nlog/5.4.0/nlog.5.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/nlog.extensions.logging/5.4.0/nlog.extensions.logging.5.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/nlog.web.aspnetcore/5.3.15/nlog.web.aspnetcore.5.3.15.nupkg.sha512", "/Users/<USER>/.nuget/packages/nuget.frameworks/5.11.0/nuget.frameworks.5.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/quartz/3.11.0/quartz.3.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/quartz.extensions.dependencyinjection/3.11.0/quartz.extensions.dependencyinjection.3.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/quartz.extensions.hosting/3.11.0/quartz.extensions.hosting.3.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system/4.3.0/runtime.native.system.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.io.compression/4.3.0/runtime.native.system.io.compression.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.net.http/4.3.0/runtime.native.system.net.http.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.security.cryptography.apple/4.3.0/runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.security.cryptography.openssl/4.3.0/runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/std.uritemplate/0.0.57/std.uritemplate.0.0.57.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore/6.5.0/swashbuckle.aspnetcore.6.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.5.0/swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.5.0/swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.5.0/swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.appcontext/4.3.0/system.appcontext.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.3.0/system.buffers.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.clientmodel/1.0.0/system.clientmodel.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections/4.3.0/system.collections.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.concurrent/4.3.0/system.collections.concurrent.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.configuration.configurationmanager/8.0.1/system.configuration.configurationmanager.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.console/4.3.0/system.console.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.debug/4.3.0/system.diagnostics.debug.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/8.0.0/system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.eventlog/8.0.1/system.diagnostics.eventlog.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tools/4.3.0/system.diagnostics.tools.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tracing/4.3.0/system.diagnostics.tracing.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization/4.3.0/system.globalization.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization.calendars/4.3.0/system.globalization.calendars.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization.extensions/4.3.0/system.globalization.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.identitymodel.tokens.jwt/7.6.0/system.identitymodel.tokens.jwt.7.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io/4.3.0/system.io.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.compression/4.3.0/system.io.compression.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.compression.zipfile/4.3.0/system.io.compression.zipfile.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem/4.3.0/system.io.filesystem.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem.primitives/4.3.0/system.io.filesystem.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq/4.3.0/system.linq.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq.expressions/4.3.0/system.linq.expressions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.4/system.memory.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory.data/1.0.2/system.memory.data.1.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.http/4.3.0/system.net.http.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.primitives/4.3.0/system.net.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.sockets/4.3.0/system.net.sockets.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.numerics.vectors/4.5.0/system.numerics.vectors.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.objectmodel/4.3.0/system.objectmodel.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection/4.3.0/system.reflection.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit/4.3.0/system.reflection.emit.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.ilgeneration/4.3.0/system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.lightweight/4.3.0/system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.extensions/4.3.0/system.reflection.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.typeextensions/4.3.0/system.reflection.typeextensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.resources.resourcemanager/4.3.0/system.resources.resourcemanager.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime/4.3.0/system.runtime.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.extensions/4.3.0/system.runtime.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.handles/4.3.0/system.runtime.handles.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices/4.3.0/system.runtime.interopservices.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices.runtimeinformation/4.3.0/system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.numerics/4.3.0/system.runtime.numerics.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.claims/4.3.0/system.security.claims.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.algorithms/4.3.0/system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.cng/4.3.0/system.security.cryptography.cng.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.csp/4.3.0/system.security.cryptography.csp.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.encoding/4.3.0/system.security.cryptography.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.openssl/4.3.0/system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.pkcs/8.0.1/system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.primitives/4.3.0/system.security.cryptography.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.protecteddata/8.0.0/system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.x509certificates/4.3.0/system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.principal/4.3.0/system.security.principal.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.extensions/4.3.0/system.text.encoding.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/4.7.2/system.text.encodings.web.4.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/4.7.2/system.text.json.4.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.regularexpressions/4.3.0/system.text.regularexpressions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading/4.3.0/system.threading.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.5.4/system.threading.tasks.extensions.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.timer/4.3.0/system.threading.timer.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.readerwriter/4.3.0/system.xml.readerwriter.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.xdocument/4.3.0/system.xml.xdocument.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit/2.4.2/xunit.2.4.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.abstractions/2.0.3/xunit.abstractions.2.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.analyzers/1.0.0/xunit.analyzers.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.assert/2.4.2/xunit.assert.2.4.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.core/2.4.2/xunit.core.2.4.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.extensibility.core/2.4.2/xunit.extensibility.core.2.4.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.extensibility.execution/2.4.2/xunit.extensibility.execution.2.4.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.runner.visualstudio/2.4.3/xunit.runner.visualstudio.2.4.3.nupkg.sha512"], "logs": []}