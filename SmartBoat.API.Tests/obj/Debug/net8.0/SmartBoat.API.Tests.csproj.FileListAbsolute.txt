/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.csproj.AssemblyReference.cache
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.AssemblyInfoInputs.cache
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.AssemblyInfo.cs
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.csproj.CoreCompileInputs.cache
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.sourcelink.json
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.deps.json
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.runtimeconfig.json
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/nuget.config
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/appsettings.Development.json
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/appsettings.Production.json
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/xunit.runner.visualstudio.dotnetcore.testadapter.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/xunit.runner.reporters.netcoreapp10.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/xunit.runner.utility.netcoreapp10.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.deps.json
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.runtimeconfig.json
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.pdb
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Azure.Core.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Azure.Identity.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/BCrypt.Net-Next.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Castle.Core.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/CsvHelper.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Dapper.StrongName.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/HtmlAgilityPack.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.AspNetCore.OpenApi.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Bcl.AsyncInterfaces.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Bcl.Cryptography.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.VisualStudio.CodeCoverage.Shim.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Data.SqlClient.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Extensions.Caching.Memory.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Binder.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Extensions.Options.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Extensions.Primitives.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Graph.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Graph.Auth.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Graph.Core.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Identity.Client.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.IdentityModel.Abstractions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.IdentityModel.Logging.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.IdentityModel.Protocols.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.IdentityModel.Tokens.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Kiota.Abstractions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Kiota.Authentication.Azure.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Kiota.Http.HttpClientLibrary.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Kiota.Serialization.Form.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Kiota.Serialization.Json.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Kiota.Serialization.Multipart.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.Kiota.Serialization.Text.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.OpenApi.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.SqlServer.Server.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.TestPlatform.CoreUtilities.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.TestPlatform.PlatformAbstractions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.TestPlatform.CommunicationUtilities.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.TestPlatform.CrossPlatEngine.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.TestPlatform.Utilities.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Microsoft.VisualStudio.TestPlatform.Common.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/testhost.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Moq.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Nbg.AspNetCore.Dapper.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Nbg.NetCore.AutocodeDbOperations.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Nbg.NetCore.Configuration.Extensions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Nbg.NetCore.Data.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Nbg.NetCore.DatabaseService.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Nbg.NetCore.Utilities.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Newtonsoft.Json.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/NLog.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/NLog.Extensions.Logging.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/NLog.Web.AspNetCore.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/NuGet.Frameworks.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Quartz.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Quartz.Extensions.DependencyInjection.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Quartz.Extensions.Hosting.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Std.UriTemplate.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Swashbuckle.AspNetCore.Swagger.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/System.ClientModel.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/System.Configuration.ConfigurationManager.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/System.Diagnostics.EventLog.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/System.IdentityModel.Tokens.Jwt.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/System.Memory.Data.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/System.Security.Cryptography.Pkcs.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/System.Security.Cryptography.ProtectedData.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/xunit.abstractions.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/xunit.assert.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/xunit.core.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/xunit.execution.dotnet.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/cs/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/de/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/es/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/fr/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/it/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ja/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ko/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pl/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ru/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/tr/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.pdb
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoa.6EC8D1B5.Up2Date
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/refint/SmartBoat.API.Tests.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.pdb
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/SmartBoat.API.Tests.genruntimeconfig.cache
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/Debug/net8.0/ref/SmartBoat.API.Tests.dll
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/testCoordsShouldNotHaveGoneOutsideFence.json
/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/testCoordsShouldHaveGoneOutsideFence.json
