{"format": 1, "restore": {"/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/SmartBoat.API.Tests.csproj": {}}, "projects": {"/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/SmartBoat.API.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/SmartBoat.API.Tests.csproj", "projectName": "SmartBoat.API.Tests", "projectPath": "/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/SmartBoat.API.Tests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/projects/smartboat-platform/SmartBoat.API/SmartBoat.API.csproj": {"projectPath": "/Users/<USER>/projects/smartboat-platform/SmartBoat.API/SmartBoat.API.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.6.0, )"}, "Moq": {"target": "Package", "version": "[4.20.69, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/projects/smartboat-platform/SmartBoat.API/SmartBoat.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/projects/smartboat-platform/SmartBoat.API/SmartBoat.API.csproj", "projectName": "SmartBoat.API", "projectPath": "/Users/<USER>/projects/smartboat-platform/SmartBoat.API/SmartBoat.API.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/projects/smartboat-platform/SmartBoat.API/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/projects/smartboat-platform/SmartBoat.API/NuGet.Config", "/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/Users/<USER>/projects/smartboat-platform/SmartBoat.API/.nugets/": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CsvHelper": {"target": "Package", "version": "[33.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.56.0, )"}, "Microsoft.Graph.Auth": {"target": "Package", "version": "[1.0.0-preview.7, )"}, "NLog": {"target": "Package", "version": "[5.4.0, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.4.0, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[5.3.15, )"}, "Nbg.AspNetCore.Dapper": {"target": "Package", "version": "[*******, )"}, "Nbg.NetCore.AutocodeDbOperations": {"target": "Package", "version": "[1.1.0-ci-20250409-144812, )"}, "Nbg.NetCore.Configuration.Extensions": {"target": "Package", "version": "[9.0.5, )"}, "Nbg.NetCore.Data": {"target": "Package", "version": "[*******, )"}, "Nbg.NetCore.DatabaseService": {"target": "Package", "version": "[8.1.5, )"}, "Nbg.NetCore.Utilities": {"target": "Package", "version": "[8.0.4, )"}, "Quartz.Extensions.Hosting": {"target": "Package", "version": "[3.11.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}