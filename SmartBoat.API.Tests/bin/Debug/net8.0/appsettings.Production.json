{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "#{ConnectionString}#"}, "Cors": {"AllowedOrigins": ["https://#{AppServiceUrl}#"], "AllowCredentials": true, "AllowAnyHeader": true, "AllowAnyMethod": true}, "Jwt": {"SecretKey": "SmartBoatSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+", "Issuer": "SmartBoat.API", "Audience": "SmartBoat.API", "ExpirationMinutes": 120}, "Security": {"Salt": "SmartBoat2024!@#SecureSalt$%^&*()"}, "Authorization": {"SuperAdminRoleName": "Super Admin"}}