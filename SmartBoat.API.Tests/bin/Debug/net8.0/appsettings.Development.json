{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=127.0.0.1,1433;Database=Smartboat;User ID=SA;Password=YourStrong@Passw0rd;TrustServerCertificate=False;Encrypt=False;Connection Timeout=30;Integrated Security=False;"}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "https://localhost:3000", "http://localhost:3001", "https://localhost:3001", "http://localhost:5173", "https://localhost:5173", "http://localhost:5174", "https://localhost:5174"], "AllowCredentials": true, "AllowAnyHeader": true, "AllowAnyMethod": true}, "Jwt": {"SecretKey": "SmartBoatSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+", "Issuer": "SmartBoat.API", "Audience": "SmartBoat.API", "ExpirationMinutes": 60}, "Security": {"Salt": "SmartBoat2024!@#SecureSalt$%^&*()"}, "Authorization": {"SuperAdminRoleName": "Super Admin"}, "MicrosoftGraph": {"TenantId": "72d7af18-45b8-4c9a-ac32-d056c6bda0f5", "ClientId": "4d59239e-ff4d-4bd9-b825-e61d87a1a6e9", "RedirectUri": "http://localhost:3000/smtp-redirect"}}