﻿<?xml version="1.0" encoding="utf-8"?>
<TestRun id="c28d32ba-1cce-479d-a66d-42d876f64e7e" name="@MacBook-Pro 2025-09-12 11:00:27" xmlns="http://microsoft.com/schemas/VisualStudio/TeamTest/2010">
  <Times creation="2025-09-12T11:00:27.8577470+03:00" queuing="2025-09-12T11:00:27.8577470+03:00" start="2025-09-12T11:00:23.4121850+03:00" finish="2025-09-12T11:00:27.8795050+03:00" />
  <TestSettings name="default" id="ea8944b5-37fb-4921-8ab9-3298ea081bfd">
    <Deployment runDeploymentRoot="_MacBook-Pro_2025-09-12_11_00_27" />
  </TestSettings>
  <Results>
    <UnitTestResult executionId="645d18ec-caf7-4219-afb2-d6e614d5ba19" testId="ff5de878-dcda-640a-1f23-8f639c365820" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_NoRecords_ShouldReturnEmptyList" computerName="MacBook-Pro" duration="00:00:00.0011952" startTime="2025-09-12T11:00:27.8485800+03:00" endTime="2025-09-12T11:00:27.8485800+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="645d18ec-caf7-4219-afb2-d6e614d5ba19" />
    <UnitTestResult executionId="0aa0bb1d-f8d2-41b1-9f3f-5ea5449b9e96" testId="d9c05749-502e-10dc-04ae-00f9701ce12f" testName="SmartBoat.API.Tests.GeoFencingServiceTests.IsInsideGeoFence_OutsideRadius_ShouldReturnFalse" computerName="MacBook-Pro" duration="00:00:00.0000808" startTime="2025-09-12T11:00:27.8486410+03:00" endTime="2025-09-12T11:00:27.8486410+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0aa0bb1d-f8d2-41b1-9f3f-5ea5449b9e96" />
    <UnitTestResult executionId="fa5ec27e-fc9e-42e2-ae8f-078bd7964a92" testId="0f376643-b85f-f6d5-9b65-f6fd9bd99fe4" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_WithStatusFilter_ShouldReturnOnlyMatchingRecords" computerName="MacBook-Pro" duration="00:00:00.0006238" startTime="2025-09-12T11:00:27.8486570+03:00" endTime="2025-09-12T11:00:27.8486570+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fa5ec27e-fc9e-42e2-ae8f-078bd7964a92" />
    <UnitTestResult executionId="5a9f1feb-ffec-4a60-ad9f-211caa27b9ac" testId="630b9990-f852-7c82-a991-55158e7054e6" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetActiveFenceEventsAsync_NoActiveEvents_ShouldReturnEmptyList" computerName="MacBook-Pro" duration="00:00:00.0007413" startTime="2025-09-12T11:00:27.8396580+03:00" endTime="2025-09-12T11:00:27.8396580+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5a9f1feb-ffec-4a60-ad9f-211caa27b9ac" />
    <UnitTestResult executionId="ed8a15b4-6378-4290-aa93-fcd3dbf52daf" testId="046838b9-731f-43b5-5151-8e4bf4c683ca" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_WithDateRange_ShouldReturnOnlyRecordsInRange" computerName="MacBook-Pro" duration="00:00:00.0297446" startTime="2025-09-12T11:00:27.8378310+03:00" endTime="2025-09-12T11:00:27.8378390+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ed8a15b4-6378-4290-aa93-fcd3dbf52daf" />
    <UnitTestResult executionId="d53e1aa8-e460-4418-990a-5ef37dc92a80" testId="fab96fb6-bad6-ff1d-0a31-dff57847156d" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_ManyRecordsWithPagination_ShouldRespectPagination" computerName="MacBook-Pro" duration="00:00:00.0007043" startTime="2025-09-12T11:00:27.8458720+03:00" endTime="2025-09-12T11:00:27.8458720+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d53e1aa8-e460-4418-990a-5ef37dc92a80" />
    <UnitTestResult executionId="4362c69b-b4eb-40aa-a74d-4bfd401406ee" testId="12396eae-3c7b-76f1-3f13-d6babe39e904" testName="SmartBoat.API.Tests.GeoFencingServiceTests.IsInsideGeoFence_AtAthensCenter_ShouldReturnTrue" computerName="MacBook-Pro" duration="00:00:00.0000583" startTime="2025-09-12T11:00:27.8424060+03:00" endTime="2025-09-12T11:00:27.8424060+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4362c69b-b4eb-40aa-a74d-4bfd401406ee" />
    <UnitTestResult executionId="5d95f800-c267-42d7-8c8a-4382df924878" testId="9e5ad756-a98a-977f-a380-0089696af27e" testName="SmartBoat.API.Tests.GeoFencingServiceTests.CalculateDistanceKm_AthensToThessaloniki_ShouldReturnCorrectDistance" computerName="MacBook-Pro" duration="00:00:00.0012721" startTime="2025-09-12T11:00:27.8389500+03:00" endTime="2025-09-12T11:00:27.8389500+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5d95f800-c267-42d7-8c8a-4382df924878" />
    <UnitTestResult executionId="1af22402-75a5-4231-85f3-e566003e2180" testId="377d55f0-a0c9-2de8-24fb-9764ed080d13" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_OneCompletedRecord_ShouldReturnOneRecordWithDuration" computerName="MacBook-Pro" duration="00:00:00.0026617" startTime="2025-09-12T11:00:27.8423360+03:00" endTime="2025-09-12T11:00:27.8423360+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1af22402-75a5-4231-85f3-e566003e2180" />
    <UnitTestResult executionId="c4eabb7c-5c96-4959-89b9-404bf92ab4d9" testId="9aec8821-d283-f219-7eb7-2dc5c3588e48" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_DatabaseReturnsNull_ShouldReturnEmptyList" computerName="MacBook-Pro" duration="00:00:00.0002479" startTime="2025-09-12T11:00:27.8435550+03:00" endTime="2025-09-12T11:00:27.8435550+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c4eabb7c-5c96-4959-89b9-404bf92ab4d9" />
    <UnitTestResult executionId="ac779ce1-24a2-478f-8425-65768d2ba8dd" testId="5c8a4241-4788-c18b-a491-16e3088b2496" testName="SmartBoat.API.Tests.GeoFencingServiceTests.IsInsideGeoFence_WithinRadius_ShouldReturnTrue" computerName="MacBook-Pro" duration="00:00:00.0000413" startTime="2025-09-12T11:00:27.8485970+03:00" endTime="2025-09-12T11:00:27.8485970+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ac779ce1-24a2-478f-8425-65768d2ba8dd" />
    <UnitTestResult executionId="2d37f522-4444-46b2-aed0-957234b10e9a" testId="a8e0c8f8-1669-bd3f-39ac-7b8c435dc4a4" testName="SmartBoat.API.Tests.GeoFencingServiceTests.CalculateDistanceKm_SameCoordinates_ShouldReturnZero" computerName="MacBook-Pro" duration="00:00:00.0000549" startTime="2025-09-12T11:00:27.8486090+03:00" endTime="2025-09-12T11:00:27.8486090+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2d37f522-4444-46b2-aed0-957234b10e9a" />
    <UnitTestResult executionId="d20822e1-9cc3-496e-8628-a0fe6f5fcdd4" testId="9aab5d4e-8fb5-19ca-b092-b0a5409717f1" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetActiveFenceEventsAsync_MultipleActiveEvents_ShouldReturnAllOrderedByExitTime" computerName="MacBook-Pro" duration="00:00:00.0015652" startTime="2025-09-12T11:00:27.8451420+03:00" endTime="2025-09-12T11:00:27.8451420+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d20822e1-9cc3-496e-8628-a0fe6f5fcdd4" />
    <UnitTestResult executionId="64f7f99f-1e65-4252-88fc-562a6070a998" testId="cd6dfb30-1a0f-e675-5a6b-9d44c227fa7c" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_ManyRecords_ShouldReturnAllRecordsOrderedByExitTime" computerName="MacBook-Pro" duration="00:00:00.0008707" startTime="2025-09-12T11:00:27.8432960+03:00" endTime="2025-09-12T11:00:27.8432960+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="64f7f99f-1e65-4252-88fc-562a6070a998" />
    <UnitTestResult executionId="11851e3f-ac98-4e7b-bc0c-59bb7bcc08eb" testId="64f5ff14-ec24-2ced-0b54-f44a0273efc8" testName="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_OneActiveRecord_ShouldReturnOneRecord" computerName="MacBook-Pro" duration="00:00:00.0006473" startTime="2025-09-12T11:00:27.8486210+03:00" endTime="2025-09-12T11:00:27.8486210+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="11851e3f-ac98-4e7b-bc0c-59bb7bcc08eb" />
  </Results>
  <TestDefinitions>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_ManyRecords_ShouldReturnAllRecordsOrderedByExitTime" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="cd6dfb30-1a0f-e675-5a6b-9d44c227fa7c">
      <Execution id="64f7f99f-1e65-4252-88fc-562a6070a998" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetVesselFenceEventsAsync_ManyRecords_ShouldReturnAllRecordsOrderedByExitTime" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_NoRecords_ShouldReturnEmptyList" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="ff5de878-dcda-640a-1f23-8f639c365820">
      <Execution id="645d18ec-caf7-4219-afb2-d6e614d5ba19" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetVesselFenceEventsAsync_NoRecords_ShouldReturnEmptyList" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.IsInsideGeoFence_WithinRadius_ShouldReturnTrue" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="5c8a4241-4788-c18b-a491-16e3088b2496">
      <Execution id="ac779ce1-24a2-478f-8425-65768d2ba8dd" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="IsInsideGeoFence_WithinRadius_ShouldReturnTrue" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.CalculateDistanceKm_SameCoordinates_ShouldReturnZero" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="a8e0c8f8-1669-bd3f-39ac-7b8c435dc4a4">
      <Execution id="2d37f522-4444-46b2-aed0-957234b10e9a" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="CalculateDistanceKm_SameCoordinates_ShouldReturnZero" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_DatabaseReturnsNull_ShouldReturnEmptyList" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="9aec8821-d283-f219-7eb7-2dc5c3588e48">
      <Execution id="c4eabb7c-5c96-4959-89b9-404bf92ab4d9" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetVesselFenceEventsAsync_DatabaseReturnsNull_ShouldReturnEmptyList" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.IsInsideGeoFence_OutsideRadius_ShouldReturnFalse" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="d9c05749-502e-10dc-04ae-00f9701ce12f">
      <Execution id="0aa0bb1d-f8d2-41b1-9f3f-5ea5449b9e96" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="IsInsideGeoFence_OutsideRadius_ShouldReturnFalse" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_WithStatusFilter_ShouldReturnOnlyMatchingRecords" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="0f376643-b85f-f6d5-9b65-f6fd9bd99fe4">
      <Execution id="fa5ec27e-fc9e-42e2-ae8f-078bd7964a92" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetVesselFenceEventsAsync_WithStatusFilter_ShouldReturnOnlyMatchingRecords" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_OneCompletedRecord_ShouldReturnOneRecordWithDuration" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="377d55f0-a0c9-2de8-24fb-9764ed080d13">
      <Execution id="1af22402-75a5-4231-85f3-e566003e2180" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetVesselFenceEventsAsync_OneCompletedRecord_ShouldReturnOneRecordWithDuration" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetActiveFenceEventsAsync_NoActiveEvents_ShouldReturnEmptyList" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="630b9990-f852-7c82-a991-55158e7054e6">
      <Execution id="5a9f1feb-ffec-4a60-ad9f-211caa27b9ac" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetActiveFenceEventsAsync_NoActiveEvents_ShouldReturnEmptyList" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_OneActiveRecord_ShouldReturnOneRecord" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="64f5ff14-ec24-2ced-0b54-f44a0273efc8">
      <Execution id="11851e3f-ac98-4e7b-bc0c-59bb7bcc08eb" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetVesselFenceEventsAsync_OneActiveRecord_ShouldReturnOneRecord" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetActiveFenceEventsAsync_MultipleActiveEvents_ShouldReturnAllOrderedByExitTime" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="9aab5d4e-8fb5-19ca-b092-b0a5409717f1">
      <Execution id="d20822e1-9cc3-496e-8628-a0fe6f5fcdd4" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetActiveFenceEventsAsync_MultipleActiveEvents_ShouldReturnAllOrderedByExitTime" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_WithDateRange_ShouldReturnOnlyRecordsInRange" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="046838b9-731f-43b5-5151-8e4bf4c683ca">
      <Execution id="ed8a15b4-6378-4290-aa93-fcd3dbf52daf" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetVesselFenceEventsAsync_WithDateRange_ShouldReturnOnlyRecordsInRange" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.GetVesselFenceEventsAsync_ManyRecordsWithPagination_ShouldRespectPagination" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="fab96fb6-bad6-ff1d-0a31-dff57847156d">
      <Execution id="d53e1aa8-e460-4418-990a-5ef37dc92a80" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="GetVesselFenceEventsAsync_ManyRecordsWithPagination_ShouldRespectPagination" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.CalculateDistanceKm_AthensToThessaloniki_ShouldReturnCorrectDistance" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="9e5ad756-a98a-977f-a380-0089696af27e">
      <Execution id="5d95f800-c267-42d7-8c8a-4382df924878" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="CalculateDistanceKm_AthensToThessaloniki_ShouldReturnCorrectDistance" />
    </UnitTest>
    <UnitTest name="SmartBoat.API.Tests.GeoFencingServiceTests.IsInsideGeoFence_AtAthensCenter_ShouldReturnTrue" storage="/users/nektariosbanousi/projects/smartboat-platform/smartboat.api.tests/bin/debug/net8.0/smartboat.api.tests.dll" id="12396eae-3c7b-76f1-3f13-d6babe39e904">
      <Execution id="4362c69b-b4eb-40aa-a74d-4bfd401406ee" />
      <TestMethod codeBase="/Users/<USER>/projects/smartboat-platform/SmartBoat.API.Tests/bin/Debug/net8.0/SmartBoat.API.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="SmartBoat.API.Tests.GeoFencingServiceTests" name="IsInsideGeoFence_AtAthensCenter_ShouldReturnTrue" />
    </UnitTest>
  </TestDefinitions>
  <TestEntries>
    <TestEntry testId="ff5de878-dcda-640a-1f23-8f639c365820" executionId="645d18ec-caf7-4219-afb2-d6e614d5ba19" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d9c05749-502e-10dc-04ae-00f9701ce12f" executionId="0aa0bb1d-f8d2-41b1-9f3f-5ea5449b9e96" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0f376643-b85f-f6d5-9b65-f6fd9bd99fe4" executionId="fa5ec27e-fc9e-42e2-ae8f-078bd7964a92" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="630b9990-f852-7c82-a991-55158e7054e6" executionId="5a9f1feb-ffec-4a60-ad9f-211caa27b9ac" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="046838b9-731f-43b5-5151-8e4bf4c683ca" executionId="ed8a15b4-6378-4290-aa93-fcd3dbf52daf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fab96fb6-bad6-ff1d-0a31-dff57847156d" executionId="d53e1aa8-e460-4418-990a-5ef37dc92a80" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="12396eae-3c7b-76f1-3f13-d6babe39e904" executionId="4362c69b-b4eb-40aa-a74d-4bfd401406ee" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9e5ad756-a98a-977f-a380-0089696af27e" executionId="5d95f800-c267-42d7-8c8a-4382df924878" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="377d55f0-a0c9-2de8-24fb-9764ed080d13" executionId="1af22402-75a5-4231-85f3-e566003e2180" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9aec8821-d283-f219-7eb7-2dc5c3588e48" executionId="c4eabb7c-5c96-4959-89b9-404bf92ab4d9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5c8a4241-4788-c18b-a491-16e3088b2496" executionId="ac779ce1-24a2-478f-8425-65768d2ba8dd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a8e0c8f8-1669-bd3f-39ac-7b8c435dc4a4" executionId="2d37f522-4444-46b2-aed0-957234b10e9a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9aab5d4e-8fb5-19ca-b092-b0a5409717f1" executionId="d20822e1-9cc3-496e-8628-a0fe6f5fcdd4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cd6dfb30-1a0f-e675-5a6b-9d44c227fa7c" executionId="64f7f99f-1e65-4252-88fc-562a6070a998" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="64f5ff14-ec24-2ced-0b54-f44a0273efc8" executionId="11851e3f-ac98-4e7b-bc0c-59bb7bcc08eb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
  </TestEntries>
  <TestLists>
    <TestList name="Results Not in a List" id="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestList name="All Loaded Results" id="19431567-8539-422a-85d7-44ee4e166bda" />
  </TestLists>
  <ResultSummary outcome="Completed">
    <Counters total="15" executed="15" passed="15" failed="0" error="0" timeout="0" aborted="0" inconclusive="0" passedButRunAborted="0" notRunnable="0" notExecuted="0" disconnected="0" warning="0" completed="0" inProgress="0" pending="0" />
    <Output>
      <StdOut>[xUnit.net 00:00:00.00] xUnit.net VSTest Adapter v2.4.3+1b45f5407b (64-bit .NET 8.0.16)
[xUnit.net 00:00:04.16]   Discovering: SmartBoat.API.Tests
[xUnit.net 00:00:04.17]   Discovered:  SmartBoat.API.Tests
[xUnit.net 00:00:04.17]   Starting:    SmartBoat.API.Tests
[xUnit.net 00:00:04.23]   Finished:    SmartBoat.API.Tests
</StdOut>
    </Output>
  </ResultSummary>
</TestRun>