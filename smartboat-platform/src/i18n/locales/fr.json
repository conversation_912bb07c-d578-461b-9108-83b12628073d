{"common": {"dashboard": "Tableau <PERSON>", "vessels": "<PERSON><PERSON><PERSON>", "sensors": "Capteurs", "customers": "Clients", "companies": "Entreprises", "owners": "<PERSON>p<PERSON><PERSON><PERSON><PERSON>", "subscriptions": "Abonnements", "profile": "Profil", "backToDashboard": "Retour au Tableau de Bord", "welcome": "Bienvenue sur la Plateforme SmartBoat", "logout": "Déconnexion", "loading": "Chargement...", "search": "Rechercher...", "save": "Enregistrer", "cancel": "Annuler", "add": "Ajouter", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression...", "view": "<PERSON><PERSON><PERSON><PERSON>", "configure": "Configurer", "active": "Actif", "inactive": "Inactif", "pending": "En attente", "status": "Statut", "notFound": "Page Non Trouvée", "dragToResize": "Glisser pour redimensionner", "email": "Email", "phone": "Téléphone", "name": "Nom", "contact": "Contact", "contactPerson": "<PERSON><PERSON>", "actions": "Actions", "close": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "create": "<PERSON><PERSON><PERSON>", "required": "* Obligatoire", "previous": "Précédent", "next": "Suivant", "showing": "Affichage", "to": "à", "of": "sur", "itemsPerPage": "Éléments par page", "showingItems": "Affichage de {{start}} à {{end}} sur {{total}} éléments", "previousPage": "<PERSON> p<PERSON>", "nextPage": "<PERSON> suivante", "goToPage": "<PERSON>er à la page {{page}}", "all": "Tous", "download": "Télécharger", "noResults": "Aucun résultat trouvé", "last24Hours": "Dernières 24 Heures", "last7Days": "7 Derniers Jours", "last30Days": "30 Derniers Jours", "customRange": "P<PERSON>", "from": "De", "selectDate": "Sélectionner une date", "searchPlaceholder": "Rechercher par nom, email ou personne de contact", "backTo": "Retour à", "lastActive": "Dernière activité", "emailAddress": "<PERSON><PERSON><PERSON>", "phoneNumber": "Numéro de Téléphone", "statistics": "Statistiques", "details": "Détails", "overview": "<PERSON><PERSON><PERSON><PERSON>", "billing": "Facturation", "unknown": "Inconnu", "blocks": "Blocs", "basicInformation": "Informations de Base", "preview": "<PERSON><PERSON><PERSON><PERSON>", "fillAllRequired": "Veuillez remplir tous les champs obligatoires", "companyId": "ID de l'Entreprise", "id": "ID", "type": "Type", "vessel": "<PERSON><PERSON><PERSON>", "location": "Emplacement", "apply": "Appliquer", "saveChanges": "Enregistrer les Modifications", "role": "R<PERSON><PERSON>", "change": "Changer", "loadingVessels": "Chargement des navires..."}, "auth": {"signIn": "Se connecter", "emailAddress": "<PERSON><PERSON><PERSON> email", "password": "Mot de passe", "rememberMe": "Se souvenir de moi", "forgotPassword": "Mot de passe oublié?", "signingIn": "Connexion en cours...", "demoAccounts": "Co<PERSON><PERSON>", "adminUser": "Administrateur", "customerUser": "Client", "enterEmailPassword": "Veuillez saisir l'email et le mot de passe.", "invalidCredentials": "Identifiants invalides. Veuillez réessayer.", "loginError": "Une erreur s'est produite lors de la connexion. Veuillez réessayer.", "resetPassword": "Réinitialiser Vot<PERSON> Mot de Passe", "resetInstructions": "Si un compte avec cet email existe, vous recevrez sous peu des instructions pour réinitialiser votre mot de passe.", "enterEmail": "Entrez votre adresse email", "sendInstructions": "Envoyer les Instructions de Réinitialisation", "sending": "Envoi en cours...", "backToLogin": "Retour à la Connexion", "sessionTimeout": "Avertissement de fin de session:", "logoutIn": "Vous serez déconnecté dans", "stayLoggedIn": "<PERSON><PERSON>"}, "dashboard": {"recentActivity": "Activité Récente", "expandAll": "<PERSON><PERSON> d<PERSON>vel<PERSON>per", "showAll": "<PERSON><PERSON> afficher", "minAgo": "minutes", "hourAgo": "heure", "hoursAgo": "heures", "newSensorAdded": "Nouveau capteur ajouté à", "subscriptionRenewed": "Abonnement renouvelé pour", "alertAnomalyDetected": "Alerte: <PERSON><PERSON><PERSON> sur", "temperatureSensor": "capteur de température", "vesselSensors": "Capteurs", "statCards": {"customers": "Clients", "companies": "Entreprises", "vessels": "<PERSON><PERSON><PERSON>", "activeSensors": "Capteurs Actifs"}}, "vessels": {"name": "Nom", "number": "<PERSON><PERSON><PERSON><PERSON>", "type": "Type", "location": "Emplacement", "startDate": "Date de Début", "endDate": "Date de Fin", "onsigners": "Embarquants", "offsigners": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastUpdated": "Dernière Mise à Jour", "details": "<PERSON><PERSON><PERSON> du Navire", "vesselInformation": "Informations du Navire", "quickActions": "Actions Rapides", "noVessels": "<PERSON><PERSON><PERSON> navire trouvé", "vesselsList": "<PERSON><PERSON><PERSON>", "addNewVessel": "Ajouter un Nouveau Navire", "editVessel": "Modifier le Navire", "createNewVessel": "Créer un Nouveau Navire", "searchVessels": "Rechercher des navires par nom, numéro ou emplacement", "noVesselsFound": "<PERSON><PERSON><PERSON> navire trouvé", "noVesselsMessage": "Essayez d'ajuster votre recherche ou vos filtres, ou ajoutez un nouveau navire.", "noVesselsMessageUser": "Essayez d'ajuster votre recherche ou vos filtres.", "allStatuses": "Tous les Statuts", "active": "Actif", "maintenance": "Maintenance", "inactive": "Inactif", "vesselNumber": "Num<PERSON><PERSON> Navire", "currentLocation": "Emplacement Actuel", "totalSensors": "Total des Capteurs", "backToVessels": "Retour aux Navires", "vesselNotFound": "Navire non trouvé", "viewDetails": "Voir les Détails", "manageSensors": "<PERSON><PERSON><PERSON> les Capteurs", "updateVessel": "Mettre à Jour le Navire", "createVessel": "<PERSON><PERSON><PERSON> un Navire", "updateVesselInfo": "Mettre à jour les informations du navire", "addNewVesselInfo": "Ajouter un nouveau navire à la plateforme", "lastUpdate": "Dernière mise à jour", "sensorOverview": "Aperçu des Capteurs", "vesselSensors": "Capteur<PERSON> <PERSON> Navire", "scheduleInteractive": "Le calendrier interactif de planification sera disponible dans les futures mises à jour", "statUnavailable": "Les statistiques seront disponibles après la création du navire", "tabs": {"vesselInfo": "Info Navire", "routeMap": "Carte d'Itinéraire", "sensors": "Capteurs"}, "blocks": {"basicInformation": "Informations de Base", "schedule": "<PERSON><PERSON><PERSON>", "scheduleDetails": "<PERSON><PERSON><PERSON>rier", "statistics": "Statistiques"}, "vesselTypes": {"mechanical": "À moteur", "sailing": "À voile"}, "confirmDelete": "Su<PERSON><PERSON><PERSON> le Navire", "deleteWarning": "Êtes-vous sûr de vouloir supprimer le navire {{vesselName}} ? Cette action ne peut pas être annulée.", "deleteFailed": "Échec de la suppression du navire. Veuillez réessayer.", "deleteVessel": "Su<PERSON><PERSON><PERSON> le Navire", "selectOwner": "Sélectionner un Propriétaire", "noOwnerSelected": "Aucun propriétaire sélectionné", "noOwnersForCompany": "Aucun propriétaire disponible pour cette entreprise", "ownerHasNoVessels": "{{ownerName}} n'a aucun navire assigné"}, "sensors": {"name": "Nom", "type": "Type", "vessel": "<PERSON><PERSON><PERSON>", "selectVessel": "Sélectionner un navire", "location": "Emplacement", "lastReading": "<PERSON><PERSON><PERSON> Lecture", "lastUpdated": "Dernière Mise à Jour", "alertThreshold": "<PERSON><PERSON>", "readings": "Lectures", "average": "<PERSON><PERSON><PERSON>", "current": "Actuelle", "minimum": "Minimum", "maximum": "Maximum", "sensorData": "<PERSON><PERSON><PERSON>", "loadingSensorData": "Chargement des données du capteur...", "fetchingSensorReadings": "Récupération des lectures du capteur...", "errorLoadingSensorData": "Échec du chargement des données du capteur", "noDataAvailable": "<PERSON><PERSON><PERSON> donn<PERSON> de capteur disponible", "time": "Temps", "sensor": "Capteur", "sensorInformation": "Informations du Capteur", "sensorReadings": "Lectures du Capteur", "quickActions": "Actions Rapides", "recalibrate": "<PERSON><PERSON><PERSON><PERSON>", "diagnosticTest": "Test Diagnostique", "noSensors": "<PERSON><PERSON>n capteur trouvé", "sensorName": "Nom du Capteur", "vesselLocation": "Navire / Emplacement", "addNewSensor": "Ajouter un Nouveau Capteur", "editSensor": "Modifier le Capteur", "createNewSensor": "<PERSON><PERSON><PERSON> un Nouveau Capteur", "searchSensors": "Rechercher des capteurs par nom, navire ou emplacement", "noSensorsMessageAdmin": "Essayez d'ajuster votre recherche ou vos filtres, ou ajoutez un nouveau capteur.", "noSensorsMessageUser": "Essayez d'ajuster votre recherche ou vos filtres.", "allTypes": "Tous les Types", "allStatuses": "Tous les Statuts", "types": {"temperature": "Température", "pressure": "Pression", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "flowRate": "Débit"}, "status": {"warning": "Avertissement", "critical": "Critique"}, "configuration": "Configuration", "blocks": "Blocs", "basicInformation": "Informations de Base", "updateSensorInfo": "Mettre à jour les informations du capteur", "addNewSensorInfo": "Ajouter un nouveau capteur à la plateforme", "readingsData": "<PERSON><PERSON><PERSON>s", "readingsUnavailable": "Les données de lectures seront disponibles après la création du capteur", "historicalReadingsUnavailable": "Le graphique des lectures historiques sera disponible dans les futures mises à jour", "updateSensor": "Mettre à Jour le Capteur", "createSensor": "<PERSON><PERSON><PERSON> un Capteur", "fillAllRequired": "Veuillez remplir tous les champs obligatoires", "sensorNotFound": "Capteur non trouvé", "backToSensors": "Retour aux Capteurs", "history": "Historique", "settings": "Paramètres", "readingHistory": "Historique des Lectures", "dateRange": "Plage de <PERSON>s", "resolution": "Résolution", "hourly": "<PERSON><PERSON><PERSON>", "daily": "Quotidien", "weekly": "Hebdomadaire", "last24Hours": "Dernières 24 heures", "last7Days": "7 derniers jours", "last30Days": "30 derniers jours", "customRange": "<PERSON><PERSON>", "timestamp": "Horodatage", "value": "<PERSON><PERSON>", "sensorSettings": "Paramètres du Capteur", "generalSettings": "Paramètres Généraux", "alertConfiguration": "Configuration des Alertes", "samplingRate": "Taux d'Échantillonnage", "every5Minutes": "Toutes les 5 minutes", "every15Minutes": "Toutes les 15 minutes", "every30Minutes": "Toutes les 30 minutes", "enableEmailAlerts": "Activer les alertes par email", "enableSmsAlerts": "Activer les alertes SMS", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON> le Capteur", "deleteWarning": "Êtes-vous sûr de vouloir supprimer le capteur {{sensorName}} ? Cette action ne peut pas être annulée.", "deleteFailed": "Échec de la suppression du capteur. Veuillez réessayer.", "deleteSensor": "<PERSON><PERSON><PERSON><PERSON> le Capteur", "errorLoadingSensors": "Échec du chargement des capteurs"}, "subscriptions": {"yourSubscriptions": "Vos Abonnements", "yourCurrentSubscription": "Votre Abonnement Actuel", "yourOtherSubscriptions": "Vos Autres Abonnements", "manageSubscriptions": "<PERSON><PERSON>rer les Abonnements", "addNewSubscription": "Ajouter un Nouvel Abonnement", "createNewSubscription": "Créer un Nouvel Abonnement", "editSubscription": "Modifier l'Abonnement", "price": "Prix", "validFrom": "Valide du", "validTo": "au", "plan": "Plan", "noSubscriptionsFound": "Aucun Abonnement Trouvé", "noSubscriptionsMessage": "Il n'y a actuellement aucun abonnement actif dans le système. Cliquez sur le bouton ci-dessous pour créer un nouvel abonnement.", "billingFrequency": "Fréquence de Facturation", "startDate": "Date de Début", "endDate": "Date de Fin", "monthly": "<PERSON><PERSON><PERSON>", "quarterly": "<PERSON><PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "status": {"active": "Actif", "pending": "En attente", "expired": "Expiré", "cancelled": "<PERSON><PERSON><PERSON>"}, "subscriptionPeriod": "Période d'Abonnement", "subscriptionNotFound": "Abonnement non trouvé", "subscriptionNotFoundMessage": "L'abonnement que vous recherchez n'existe pas ou vous n'avez pas la permission de le consulter.", "backToSubscriptions": "Retour aux Abonnements", "errorLoadingSubscription": "Erreur lors du chargement de l'abonnement", "features": "Fonctionnalités", "paymentInformation": "Informations de Paiement", "nextBillingDate": "Prochaine date de facturation", "paymentMethod": "Méthode de paiement", "updateSubscriptionDetails": "Mettre à jour les détails de l'abonnement", "addNewSubscriptionInfo": "Ajouter un nouveau plan d'abonnement pour un client", "billingDetails": "Détails de Facturation", "planFeatures": "Fonctionnalités du Plan", "subscriptionName": "Nom de l'Abonnement", "subscriptionType": "Type d'Abonnement", "selectCustomer": "Sélectionner un Client", "selectVessel": "Sélectionner un Navire", "errorFetchingVessels": "Échec du chargement des navires. Veuillez réessayer.", "pricePerSensor": "Prix Par Capteur", "sensorLimit": "Limite de Capteurs par Navire", "unlimitedSensorsInfo": "Laissez vide pour des capteurs illimités", "featuresPerLine": "Fonctionnalités (une par ligne)", "enterFeaturesPlaceholder": "Entrez les fonctionnalités, une par ligne", "featuresInstructionText": "Listez les fonctionnalités incluses dans ce plan d'abonnement, avec chaque fonctionnalité sur une nouvelle ligne.", "updateSubscription": "Mettre à Jour l'Abonnement", "createSubscription": "C<PERSON>er un Abonnement", "planTypes": {"standard": "Standard", "professional": "Professionnel", "enterprise": "Entreprise", "custom": "<PERSON><PERSON><PERSON><PERSON>"}}, "notFound": {"title": "404", "subtitle": "Page Non Trouvée", "message": "La page n'existe pas ou vous n'avez pas la permission d'y accéder.", "backToDashboard": "<PERSON><PERSON> <PERSON>au <PERSON>", "goBack": "Retour"}, "profile": {"userProfile": "<PERSON><PERSON>", "personalInformation": "Informations Personnelles", "accountSettings": "Paramètres du Compte", "notifications": "Notifications", "language": "<PERSON><PERSON>", "english": "<PERSON><PERSON><PERSON>", "greek": "Grec", "french": "Français", "changePassword": "Changer <PERSON> de Passe", "currentPassword": "Mot de Passe Actuel", "newPassword": "Nouveau Mot de Passe", "confirmPassword": "Confirm<PERSON> le Mot de Passe", "saveChanges": "Enregistrer les Modifications", "editProfile": "Modifier le Profil", "joined": "Inscrit le", "lastLogin": "Dernière connexion", "fullName": "Nom Co<PERSON>t", "bio": "Biographie", "workInformation": "Informations Professionnelles", "company": "Entreprise", "department": "Département", "socialLinks": "<PERSON><PERSON>", "linkedinUrl": "URL LinkedIn", "twitterUrl": "URL Twitter", "githubUrl": "URL GitHub", "notProvided": "Non fourni", "securitySettings": "Paramètres de Sécurité", "twoFactorAuth": "Authentification à Deux Facteurs", "twoFactorStatus": "L'authentification à deux facteurs est", "enabled": "activée", "notEnabled": "non activée", "forYourAccount": "pour votre compte", "enable": "Activer", "disable": "Désactiver", "changeYourPassword": "Changer votre mot de passe", "updatePassword": "Mettre à Jour le Mot de Passe", "loginSessions": "Sessions de Connexion", "currentSession": "Session Actuelle", "started": "<PERSON><PERSON><PERSON><PERSON>", "signOutAllSessions": "Se déconnecter de toutes les autres sessions", "accountActions": "Actions du Compte", "deactivateAccount": "Désactiver le compte", "languageSetEnglish": "La langue de l'application est définie en Anglais", "languageSetGreek": "La langue de l'application est définie en Grec", "languageSetFrench": "La langue de l'application est définie en Français", "change": "Changer", "role": "R<PERSON><PERSON>"}, "customers": {"customersList": "Clients", "addNewCustomer": "Ajouter un Nouveau Client", "editCustomer": "Modifier le Client", "createNewCustomer": "Créer un Nouveau Client", "searchCustomers": "Rechercher des clients par nom, personne de contact ou email", "noCustomersFound": "Aucun client trouvé", "noCustomersMessage": "Essayez d'ajuster votre recherche ou ajoutez un nouveau client.", "customerDetails": "Détails du Client", "companyInformation": "Informations de l'Entreprise", "primaryContact": "Contact Principal", "subscriptionOverview": "Aperçu de l'Abonnement", "viewVessels": "Voir les Navires", "backToCustomers": "Retour aux Clients", "customerName": "Nom du Client", "fillAllRequired": "Veuillez remplir tous les champs obligatoires", "basicInformation": "Informations de Base", "contactDetails": "<PERSON><PERSON><PERSON> du Contact", "updateCustomerInfo": "Mettre à jour les informations du client", "addNewCustomerInfo": "Ajouter un nouveau client à la plateforme", "blocks": "BLOCS", "statUnavailable": "Les statistiques seront disponibles après la création du client", "updateCustomer": "Mettre à Jour le Client", "createCustomer": "<PERSON><PERSON>er un Client", "customerNotFound": "Client non trouvé", "deleteCustomer": "Supprimer le Client", "confirmDelete": "Confirmer la Suppression", "deleteWarning": "Êtes-vous sûr de vouloir supprimer le client {{customerName}} ? Cette action ne peut pas être annulée.", "deleteFailed": "Échec de la suppression du client. Veuillez réessayer.", "recentActivity": "Activité Récente", "subscription": {"currentPlan": "Plan Actuel", "changePlan": "Changer de Plan", "enterprise": "Entreprise", "unlimited": "Illimité", "allowed": "autorisé", "billingInformation": "Informations de Facturation", "updatePaymentMethod": "Mettre à Jour la Méthode de Paiement", "billingHistory": "Historique de Facturation", "invoiceNumber": "Numéro de Facture", "date": "Date", "amount": "<PERSON><PERSON>", "paid": "<PERSON><PERSON>", "subscriptionAndBilling": "Abonnement & Facturation", "errorLoading": "Erreur lors du chargement des abonnements", "sensorLimit": "limite de capteurs", "noSubscriptions": "Aucun abonnement disponible", "subscriptionDetails": "Détails de l'Abonnement", "noSubscriptionDetails": "Aucun détail d'abonnement disponible"}, "tabs": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "companies": "Entreprises", "vessels": "<PERSON><PERSON><PERSON>", "subscriptionBilling": "Abonnement & Facturation"}, "companies": {"addCompany": "Ajouter une Entreprise", "noCompanies": "Aucune entreprise", "noCompaniesMessage": "Commencez par ajouter une entreprise à ce client."}, "vessels": {"allVessels": "Tous les Navires (toutes entreprises confondues)", "addVesselToCompany": "Ajouter un Navire à l'Entreprise", "unknownCompany": "Entreprise Inconnue", "noVesselsFound": "<PERSON><PERSON><PERSON> navire trouvé", "noVesselsMessage": "Aucune des entreprises de ce client n'a encore de navires.", "viewDetails": "Voir les Détails", "needCompanyFirst": "Ajou<PERSON>z d'abord une entreprise", "needCompanyMessage": "V<PERSON> devez ajouter une entreprise avant de pouvoir ajouter des navires.", "addCompanyFirst": "Ajouter une Entreprise d'Abord", "addVesselFirst": "Ajouter un Navire d'Abord"}, "errors": {"duplicateName": "Un client avec ce nom existe déjà. Veuillez choisir un nom différent.", "duplicateEmail": "Cette adresse e-mail est déjà utilisée par un autre client. Veuillez utiliser une adresse e-mail différente.", "nameAlreadyExists": "Ce nom de client est déjà pris. Veuillez utiliser un nom différent.", "technicalError": "Une erreur technique s'est produite lors du traitement de votre demande. Veuillez réessayer plus tard.", "validationFailed": "Les informations fournies ne sont pas valides. Veuillez vérifier votre saisie et réessayer.", "operationFailed": "L'opération a échoué. Veuillez réessayer ou contacter le support si le problème persiste.", "nameRequired": "Le nom du client est requis.", "nameTooShort": "Le nom du client doit contenir au moins 2 caractères."}}, "companies": {"companiesList": "Entreprises", "addNewCompany": "Ajouter une Nouvelle Entreprise", "editCompany": "Modifier l'Entreprise", "createNewCompany": "Créer une Nouvelle Entreprise", "searchCompanies": "Rechercher des entreprises par nom ou emplacement", "noCompaniesFound": "Aucune entreprise trouvée", "noCompaniesMessage": "Essayez d'ajuster votre recherche ou vos filtres, ou ajoutez une nouvelle entreprise.", "noCompaniesMessageUser": "Essayez d'ajuster votre recherche ou vos filtres pour trouver des entreprises.", "companyInformation": "Informations de l'Entreprise", "companyDetails": "Détails de l'Entreprise", "companyName": "Nom de l'Entreprise", "industry": "Industrie", "location": "Emplacement", "locationDetails": "Détails de l'Emplacement", "updateCompanyInfo": "Mettre à jour les informations de l'entreprise", "addNewCompanyInfo": "Ajouter une nouvelle entreprise à la plateforme", "statUnavailable": "Les statistiques seront disponibles après la création de l'entreprise", "updateCompany": "Mettre à Jour l'Entreprise", "createCompany": "Créer une Entreprise", "companyNotFound": "Entreprise non trouvée", "backToCompanies": "Retour aux Entreprises", "deleteCompany": "Supprimer l'Entreprise", "confirmDelete": "Confirmer la Suppression", "deleteWarning": "Êtes-vous sûr de vouloir supprimer {{companyName}} ? Cette action ne peut pas être annulée.", "deleteFailed": "Échec de la suppression de l'entreprise. Veuillez réessayer.", "companyVessels": "Navires de l'Entreprise", "noVesselsFound": "<PERSON><PERSON><PERSON> navire trouvé", "noVesselsMessage": "Cette entreprise n'a pas encore de navires.", "addNewVessel": "Ajouter un Nouveau Navire", "companyOwners": "Propriétaires de l'Entreprise", "addNewOwner": "Ajouter un Nouveau Propriétaire", "noOwnersFound": "Aucun propriétaire trouvé", "noOwnersMessage": "Cette entreprise n'a pas encore de propriétaires.", "companyPerformance": "Performance de l'Entreprise", "quickActions": "Actions Rapides", "generateReport": "Générer un Rapport", "tabs": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "vessels": "<PERSON><PERSON><PERSON>", "owners": "<PERSON>p<PERSON><PERSON><PERSON><PERSON>", "contracts": "Contrats"}, "stats": {"activeVessels": "Navires Actifs", "totalVessels": "Total des Navires", "ofFleet": "de la flotte", "inFleet": "dans la flotte", "sensorAlertRate": "Taux d'Alerte des Capteurs", "onTimePerformance": "Performance de Ponctualité", "maintenanceRate": "Taux de <PERSON>", "fromLastWeek": "depuis la semaine derni<PERSON>", "fromLastMonth": "depuis le mois dernier", "fromTarget": "par rapport à l'objectif"}, "contracts": {"contracts": "Contrats", "addNewContract": "Ajouter un Nouveau Contrat", "validUntil": "<PERSON><PERSON> jusqu'au", "active": "Actif", "renewing": "Renouvellement"}, "nameRequired": "Le nom de l'entreprise est requis", "createFailed": "Échec de la création de l'entreprise. Veuillez réessayer.", "selectCustomer": "Sélectionner un Client", "assignToCustomer": "Assigner au Client", "customerRequired": "La sélection du client est requise", "assignedCustomer": "<PERSON><PERSON>"}, "owners": {"ownersList": "<PERSON>p<PERSON><PERSON><PERSON><PERSON>", "addNewOwner": "Ajouter un Nouveau Propriétaire", "editOwner": "Modifier le Propriétaire", "createNewOwner": "<PERSON><PERSON>er un Nouveau Propriétaire", "searchOwners": "Rechercher des propriétaires par nom, email ou téléphone", "noOwnersFound": "Aucun propriétaire trouvé", "noOwnersMessage": "Essayez d'ajuster votre recherche ou vos filtres, ou ajoutez un nouveau propriétaire.", "noOwnersMessageUser": "Essayez d'ajuster votre recherche ou vos filtres pour trouver des propriétaires.", "ownerInformation": "Informations du Propriétaire", "ownerDetails": "<PERSON><PERSON><PERSON> du Propriétaire", "ownerName": "Nom du Propriétaire", "basicInformation": "Informations de Base", "contactDetails": "Coordonnées", "companyAssignment": "Assignation d'Entreprise", "email": "Email", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "company": "Entreprise", "updateOwnerInfo": "Mettre à jour les informations du propriétaire", "addNewOwnerInfo": "Ajouter un nouveau propriétaire à la plateforme", "updateOwner": "Mettre à Jour le Propriétaire", "createOwner": "<PERSON><PERSON>er un Propriétaire", "ownerNotFound": "Propriétaire non trouvé", "backToOwners": "Retour aux Propriétaires", "deleteOwner": "Su<PERSON><PERSON>er le Propriétaire", "confirmDelete": "Confirmer la Suppression", "deleteWarning": "Êtes-vous sûr de vouloir supprimer {{ownerName}} ? Cette action ne peut pas être annulée.", "deleteFailed": "Échec de la suppression du propriétaire. Veuillez réessayer.", "createFailed": "Échec de la création du propriétaire. Veuillez réessayer.", "updateFailed": "Échec de la mise à jour du propriétaire. Veuillez réessayer.", "nameRequired": "Le nom du propriétaire est requis", "companyRequired": "La sélection de l'entreprise est requise", "selectCompany": "Sélectionner une Entreprise", "selectOwner": "Sélectionner un Propriétaire", "noOwnerSelected": "Aucun propriétaire sélectionné", "noOwnersForCompany": "Aucun propriétaire trouvé pour cette entreprise", "assignToCompany": "Assigner à l'Entreprise", "assignedCompany": "Entreprise Assignée", "selectedCompany": "Entreprise Sélectionnée", "enterOwnerName": "Entrez le nom du propriétaire", "enterEmail": "Entrez l'adresse email", "enterPhone": "Entrez le numéro de téléphone", "enterAddress": "Entrez l'adresse"}, "notifications": {"title": "Notifications", "noNotifications": "Aucune notification", "noNotificationsMessage": "Vous n'avez aucune notification pour le moment.", "markAllRead": "<PERSON><PERSON> tout comme lu", "markingAllRead": "Mar<PERSON> tout comme lu...", "viewAll": "Voir toutes les notifications", "justNow": "à l'instant", "minutesAgo": "il y a {{count}} minutes", "hoursAgo": "il y a {{count}} heures", "daysAgo": "il y a {{count}} jours", "unreadCount": "{{count}} non lues sur {{total}} au total", "unreadCountOnly": "{{count}} non lues", "allRead": "Toutes les notifications ont été lues", "types": {"info": "Information", "success": "Su<PERSON>ès", "warning": "Avertissement", "error": "<PERSON><PERSON><PERSON>", "alert": "<PERSON><PERSON><PERSON>"}, "categories": {"system": "Système", "sensor": "Capteur", "vessel": "<PERSON><PERSON><PERSON>", "subscription": "Abonnement", "maintenance": "Maintenance"}}, "emailProcessing": {"title": "Traitement des E-mails", "microsoftLogin": "Connexion Microsoft", "tokenStatus": "État du Token", "lastSync": "Dernière Synchronisation", "nextSync": "Prochaine Synchronisation", "connectToMicrosoft": "Se connecter à Microsoft", "connected": "Connecté", "notConnected": "Non Connecté", "tokenExpires": "Le token expire", "reAuthRequired": "Ré-authentification requise", "syncSuccessful": "<PERSON><PERSON><PERSON><PERSON>", "syncFailed": "É<PERSON><PERSON>e", "syncPending": "En attente", "never": "<PERSON><PERSON>"}, "wlpTemplates": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> vos fichiers de modèles de géobarrière Wialon pour l'analyse des navires", "uploadTemplate": "Télécharger Mo<PERSON>", "uploadNewTemplate": "Télécharger Nouveau Modèle", "templateName": "Nom du Modèle", "templateNamePlaceholder": "Entrez le nom du modèle", "templateDescription": "Description", "templateDescriptionPlaceholder": "Entrez une description optionnelle", "selectWlpFile": "Sélectionner Fichier WLP", "fileRequirements": "Seuls les fichiers .wlp sont supportés, maximum 10MB", "existingTemplates": "<PERSON><PERSON><PERSON><PERSON>", "noTemplates": "<PERSON><PERSON><PERSON>", "noTemplatesDescription": "Téléchargez votre premier modèle WLP pour commencer l'analyse de géobarrière", "zones": "zones", "fileInfo": "<PERSON><PERSON> <PERSON> Fichier", "uploaded": "Télécharg<PERSON>", "by": "par", "confirmDelete": "Êtes-vous sûr de vouloir supprimer '{{name}}' ? Cette action ne peut pas être annulée.", "nameRequired": "Le nom du modèle est requis", "invalidFileType": "Veuillez sélectionner un fichier .wlp valide", "fileTooLarge": "La taille du fichier doit être inférieure à 10MB", "fileReadError": "Erreur de lecture du fichier. Veuillez réessayer.", "uploadSuccess": "<PERSON><PERSON><PERSON><PERSON> avec succès", "uploadError": "Échec du téléchargement du modèle. Veuillez réessayer.", "deleteSuccess": "Mod<PERSON>le supprimé avec succès", "deleteError": "Échec de la suppression du modèle. Veuillez réessayer.", "loadError": "Échec du chargement des modèles. Veuillez actualiser la page.", "uploadingTemplate": "Téléchargement du modèle..."}, "geofenceAnalysis": {"title": "<PERSON><PERSON><PERSON>", "description": "Analysez les mouvements des navires par rapport aux modèles de géobarrière WLP pour identifier les franchissements de limites et les événements de conformité", "newAnalysis": "Nouvelle Analyse", "createAnalysis": "<PERSON><PERSON><PERSON> une Analyse", "creatingAnalysis": "Création de l'Analyse...", "analysisName": "Nom de l'Analyse", "analysisNamePlaceholder": "Nom optionnel pour cette analyse", "selectVessel": "Sélectionner un Navire", "chooseVessel": "Choisissez un navire...", "selectTemplates": "Sélectionner des Modèles WLP", "dateFrom": "Date de Début", "dateTo": "Date de Fin", "analysisJobs": "Tâches d'Analyse", "noAnalysisJobs": "Aucune Tâche d'Analyse", "noAnalysisJobsDescription": "<PERSON><PERSON>ez votre première analyse de géobarrière pour commencer", "noTemplatesAvailable": "Aucun modèle WLP disponible. Veuillez d'abord télécharger des modèles dans votre profil.", "vessel": "<PERSON><PERSON><PERSON>", "templates": "<PERSON><PERSON><PERSON><PERSON>", "dateRange": "Plage de <PERSON>s", "status": "Statut", "results": "Résultats", "created": "<PERSON><PERSON><PERSON>", "coordinates": "coordonnées", "events": "événements", "to": "à", "vesselRequired": "Veuillez sélectionner un navire", "templatesRequired": "Veuillez sélectionner au moins un modèle WLP", "datesRequired": "Veuillez sélectionner une plage de dates", "invalidDateRange": "La date de fin doit être postérieure à la date de début", "createSuccess": "Tâche d'analyse créée avec succès", "createError": "Échec de la création de la tâche d'analyse. Veuillez réessayer.", "jobCompleted": "Analyse terminée avec succès", "jobFailed": "<PERSON><PERSON><PERSON> avec des erreurs", "jobRunning": "Analyse en cours d'exécution", "jobPending": "Analyse en attente de démarrage", "viewResults": "Voir les Résultats", "downloadResults": "Télécharger les Résultats", "cancelJob": "Annuler la Tâche", "deleteJob": "Supp<PERSON>er la Tâche"}}