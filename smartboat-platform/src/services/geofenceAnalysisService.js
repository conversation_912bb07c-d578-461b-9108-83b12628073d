import apiClient from './apiClient';

class GeofenceAnalysisService {
  constructor() {
    this.endpoint = '/api/geofence-analysis';
  }

  /**
   * Get all geofence analysis jobs for the current user
   * @param {Object} request - Request parameters
   * @returns {Promise<Array>} Array of analysis jobs
   */
  async getAnalysisJobs(request = {}) {
    try {
      const response = await apiClient.post(`${this.endpoint}/jobs/list`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: request
      });

      return response.data?.payload || [];
    } catch (error) {
      console.error('Error fetching analysis jobs:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch analysis jobs');
    }
  }

  /**
   * Get a specific analysis job by ID
   * @param {string} jobId - Job ID
   * @returns {Promise<Object>} Analysis job data
   */
  async getAnalysisJob(jobId) {
    try {
      const response = await apiClient.get(`${this.endpoint}/jobs/${jobId}`, {
        params: {
          userId: apiClient.getCurrentUserId()
        }
      });

      return response.data?.payload || null;
    } catch (error) {
      console.error('Error fetching analysis job:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch analysis job');
    }
  }

  /**
   * Create a new geofence analysis job
   * @param {Object} jobData - Job configuration
   * @param {string} jobData.vesselId - Vessel ID
   * @param {string[]} jobData.templateIds - Array of WLP template IDs
   * @param {string} jobData.analysisDateFrom - Start date (ISO string)
   * @param {string} jobData.analysisDateTo - End date (ISO string)
   * @param {string} [jobData.name] - Optional job name
   * @returns {Promise<Object>} Created job data
   */
  async createAnalysisJob(jobData) {
    try {
      const response = await apiClient.post(`${this.endpoint}/jobs/create`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: {
          vesselId: jobData.vesselId,
          templateIds: jobData.templateIds,
          analysisDateFrom: jobData.analysisDateFrom,
          analysisDateTo: jobData.analysisDateTo,
          name: jobData.name
        }
      });

      return response.data?.payload || null;
    } catch (error) {
      console.error('Error creating analysis job:', error);
      throw new Error(error.response?.data?.message || 'Failed to create analysis job');
    }
  }

  /**
   * Cancel a running analysis job
   * @param {string} jobId - Job ID to cancel
   * @returns {Promise<boolean>} Success status
   */
  async cancelAnalysisJob(jobId) {
    try {
      await apiClient.post(`${this.endpoint}/jobs/${jobId}/cancel`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: {}
      });

      return true;
    } catch (error) {
      console.error('Error cancelling analysis job:', error);
      throw new Error(error.response?.data?.message || 'Failed to cancel analysis job');
    }
  }

  /**
   * Delete an analysis job and its results
   * @param {string} jobId - Job ID to delete
   * @returns {Promise<boolean>} Success status
   */
  async deleteAnalysisJob(jobId) {
    try {
      await apiClient.delete(`${this.endpoint}/jobs/${jobId}`, {
        params: {
          userId: apiClient.getCurrentUserId()
        }
      });

      return true;
    } catch (error) {
      console.error('Error deleting analysis job:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete analysis job');
    }
  }

  /**
   * Get geofence events for a specific analysis job
   * @param {string} jobId - Job ID
   * @param {Object} [filters] - Optional filters
   * @returns {Promise<Array>} Array of geofence events
   */
  async getJobEvents(jobId, filters = {}) {
    try {
      const response = await apiClient.post(`${this.endpoint}/jobs/${jobId}/events`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: filters
      });

      return response.data?.payload || [];
    } catch (error) {
      console.error('Error fetching job events:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch job events');
    }
  }

  /**
   * Get summary statistics for an analysis job
   * @param {string} jobId - Job ID
   * @returns {Promise<Object>} Job summary statistics
   */
  async getJobSummary(jobId) {
    try {
      const response = await apiClient.get(`${this.endpoint}/jobs/${jobId}/summary`, {
        params: {
          userId: apiClient.getCurrentUserId()
        }
      });

      return response.data?.payload || null;
    } catch (error) {
      console.error('Error fetching job summary:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch job summary');
    }
  }

  /**
   * Get analysis results for a vessel across multiple jobs
   * @param {string} vesselId - Vessel ID
   * @param {Object} [options] - Query options
   * @returns {Promise<Array>} Array of analysis results
   */
  async getVesselAnalysis(vesselId, options = {}) {
    try {
      const response = await apiClient.post(`${this.endpoint}/vessel/${vesselId}/analysis`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: options
      });

      return response.data?.payload || [];
    } catch (error) {
      console.error('Error fetching vessel analysis:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch vessel analysis');
    }
  }

  /**
   * Get combined analysis results for multiple vessels
   * @param {string[]} vesselIds - Array of vessel IDs
   * @param {Object} [options] - Query options
   * @returns {Promise<Object>} Combined analysis results
   */
  async getCombinedAnalysis(vesselIds, options = {}) {
    try {
      const response = await apiClient.post(`${this.endpoint}/vessels/combined-analysis`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: {
          vesselIds,
          ...options
        }
      });

      return response.data?.payload || null;
    } catch (error) {
      console.error('Error fetching combined analysis:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch combined analysis');
    }
  }

  /**
   * Export analysis results to various formats
   * @param {string} jobId - Job ID
   * @param {string} format - Export format ('csv', 'excel', 'json')
   * @returns {Promise<Blob>} Export file blob
   */
  async exportAnalysisResults(jobId, format = 'csv') {
    try {
      const response = await apiClient.get(`${this.endpoint}/jobs/${jobId}/export`, {
        params: {
          userId: apiClient.getCurrentUserId(),
          format: format
        },
        responseType: 'blob'
      });

      return response.data;
    } catch (error) {
      console.error('Error exporting analysis results:', error);
      throw new Error(error.response?.data?.message || 'Failed to export analysis results');
    }
  }

  /**
   * Get job execution logs
   * @param {string} jobId - Job ID
   * @returns {Promise<Array>} Array of log entries
   */
  async getJobLogs(jobId) {
    try {
      const response = await apiClient.get(`${this.endpoint}/jobs/${jobId}/logs`, {
        params: {
          userId: apiClient.getCurrentUserId()
        }
      });

      return response.data?.payload || [];
    } catch (error) {
      console.error('Error fetching job logs:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch job logs');
    }
  }

  /**
   * Validate analysis parameters before creating job
   * @param {Object} jobData - Job configuration to validate
   * @returns {Promise<Object>} Validation result
   */
  async validateAnalysisParams(jobData) {
    try {
      const response = await apiClient.post(`${this.endpoint}/validate`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: jobData
      });

      return response.data?.payload || { isValid: false, errors: ['Unknown validation error'] };
    } catch (error) {
      console.error('Error validating analysis params:', error);
      throw new Error(error.response?.data?.message || 'Failed to validate analysis parameters');
    }
  }

  /**
   * Get analysis job statistics for dashboard
   * @returns {Promise<Object>} Dashboard statistics
   */
  async getAnalysisStatistics() {
    try {
      const response = await apiClient.get(`${this.endpoint}/statistics`, {
        params: {
          userId: apiClient.getCurrentUserId()
        }
      });

      return response.data?.payload || null;
    } catch (error) {
      console.error('Error fetching analysis statistics:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch analysis statistics');
    }
  }

  /**
   * Format job status for display
   * @param {string} status - Job status
   * @returns {Object} Formatted status with color and label
   */
  getJobStatusDisplay(status) {
    const statusMap = {
      'pending': { 
        label: 'Pending', 
        color: 'yellow',
        description: 'Job is queued and waiting to be processed'
      },
      'running': { 
        label: 'Running', 
        color: 'blue',
        description: 'Job is currently being processed'
      },
      'completed': { 
        label: 'Completed', 
        color: 'green',
        description: 'Job completed successfully'
      },
      'failed': { 
        label: 'Failed', 
        color: 'red',
        description: 'Job failed due to an error'
      },
      'cancelled': { 
        label: 'Cancelled', 
        color: 'gray',
        description: 'Job was cancelled by user'
      }
    };

    return statusMap[status?.toLowerCase()] || {
      label: status || 'Unknown',
      color: 'gray',
      description: 'Unknown status'
    };
  }

  /**
   * Estimate job processing time
   * @param {Object} jobParams - Job parameters
   * @returns {Object} Time estimation
   */
  estimateProcessingTime(jobParams) {
    const { vesselId, templateIds, analysisDateFrom, analysisDateTo } = jobParams;
    
    if (!vesselId || !templateIds?.length || !analysisDateFrom || !analysisDateTo) {
      return { estimated: null, factors: [] };
    }

    const daysDiff = Math.ceil(
      (new Date(analysisDateTo) - new Date(analysisDateFrom)) / (1000 * 60 * 60 * 24)
    );
    
    // Rough estimation based on experience
    const baseTimePerDay = 2; // seconds
    const templateMultiplier = templateIds.length;
    const estimatedSeconds = daysDiff * baseTimePerDay * templateMultiplier;
    
    return {
      estimated: Math.max(estimatedSeconds, 10), // Minimum 10 seconds
      factors: [
        `${daysDiff} days of data`,
        `${templateIds.length} template(s)`,
        'Coordinate density'
      ]
    };
  }
}

// Export singleton instance
const geofenceAnalysisService = new GeofenceAnalysisService();
export default geofenceAnalysisService;