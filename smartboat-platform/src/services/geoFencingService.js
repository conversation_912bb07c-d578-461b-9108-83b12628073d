/**
 * Geo-fencing API Service
 * Handles API requests related to vessel geo-fencing events
 */

import apiClient from './apiClient';
import { FEATURES } from '../config/envConfig';
import { mockVesselFenceEvents, mockActiveFenceEvents } from '../utils/mockData';

const BASE_ENDPOINT = '/api/vesselfenceevent';

const geoFencingService = {
  /**
   * Get fence events for a specific vessel
   * @param {Object} params - Request parameters
   * @param {string} params.vesselId - Vessel ID
   * @param {number} [params.pageLimit=25] - Number of results per page
   * @param {number} [params.pageOffset=0] - Offset for pagination
   * @param {string} [params.status] - Filter by status ("Active", "Completed", or null for all)
   * @param {Date} [params.fromDate] - Filter events from this date
   * @param {Date} [params.toDate] - Filter events until this date
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Array of vessel fence events
   */
  getVesselFenceEvents: async (params, options = {}) => {
    try {
      const requestBody = {
        vesselId: params.vesselId,
        pageLimit: params.pageLimit || 25,
        pageOffset: params.pageOffset || 0,
        ...(params.status && { status: params.status }),
        ...(params.fromDate && { fromDate: params.fromDate.toISOString() }),
        ...(params.toDate && { toDate: params.toDate.toISOString() })
      };

      const response = await apiClient.post(`${BASE_ENDPOINT}/vessel-events`, requestBody, {
        ...options,
        useMockFallback: FEATURES.USE_MOCK_FALLBACK
      });

      // Handle API response format: { payload: VesselFenceEventDto[] }
      if (response && response.payload && Array.isArray(response.payload)) {
        return response.payload;
      }

      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response;
      }

      console.warn('Unexpected fence events response format:', response);
      return [];
    } catch (error) {
      console.error('Failed to fetch vessel fence events:', error);
      
      // Return mock data as fallback
      if (FEATURES.USE_MOCK_FALLBACK) {
        console.log('Using mock vessel fence events as fallback');
        return mockVesselFenceEvents.filter(event => 
          !params.vesselId || event.vesselId === params.vesselId
        );
      }
      
      throw error;
    }
  },

  /**
   * Get all active fence events (vessels currently outside fence)
   * @param {Object} options - Request options
   * @returns {Promise<Array>} Array of active fence events
   */
  getActiveFenceEvents: async (options = {}) => {
    try {
      const response = await apiClient.post(`${BASE_ENDPOINT}/active-events`, {}, {
        ...options,
        useMockFallback: FEATURES.USE_MOCK_FALLBACK
      });

      // Handle API response format: { payload: VesselFenceEventDto[] }
      if (response && response.payload && Array.isArray(response.payload)) {
        return response.payload;
      }

      // Handle direct array response (fallback or mock data)
      if (Array.isArray(response)) {
        return response;
      }

      console.warn('Unexpected active fence events response format:', response);
      return [];
    } catch (error) {
      console.error('Failed to fetch active fence events:', error);
      
      // Return mock data as fallback
      if (FEATURES.USE_MOCK_FALLBACK) {
        console.log('Using mock active fence events as fallback');
        return mockActiveFenceEvents;
      }
      
      throw error;
    }
  },

  /**
   * Check if coordinates are inside the geo-fence (utility method for testing)
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Result indicating if coordinates are inside fence
   */
  checkCoordinates: async (lat, lng, options = {}) => {
    try {
      const requestBody = {
        lat: lat,
        lng: lng
      };

      const response = await apiClient.post(`${BASE_ENDPOINT}/check-coordinates`, requestBody, {
        ...options,
        useMockFallback: FEATURES.USE_MOCK_FALLBACK
      });

      // Handle API response format: { payload: CheckCoordinatesResultDto }
      if (response && response.payload) {
        return response.payload;
      }

      // Handle direct response (fallback or mock data)
      return response;
    } catch (error) {
      console.error('Failed to check coordinates:', error);
      throw error;
    }
  },

  /**
   * Format fence event duration for display
   * @param {number} durationMinutes - Duration in minutes
   * @returns {string} Formatted duration string
   */
  formatDuration: (durationMinutes) => {
    if (!durationMinutes) return 'N/A';

    if (durationMinutes < 60) {
      return `${durationMinutes}m`;
    }

    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;

    if (hours < 24) {
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }

    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;

    if (remainingHours > 0) {
      return `${days}d ${remainingHours}h`;
    }

    return `${days}d`;
  },

  /**
   * Format fence event status for display
   * @param {string} status - Status ("Active" or "Completed")
   * @returns {string} Display-friendly status
   */
  formatStatus: (status) => {
    switch (status) {
      case 'Active':
        return 'Outside Fence';
      case 'Completed':
        return 'Returned to Fence';
      default:
        return status || 'Unknown';
    }
  }
};

export default geoFencingService;