import apiClient from './apiClient';

class WlpTemplateService {
  constructor() {
    this.endpoint = '/api/wlp-templates';
  }

  /**
   * Get all WLP templates for the current user
   * @param {Object} request - Request parameters (searchTerm, etc.)
   * @returns {Promise<Array>} Array of WLP templates
   */
  async getTemplates(request = {}) {
    try {
      const response = await apiClient.post(`${this.endpoint}/list`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: request
      });

      return response.data?.payload || [];
    } catch (error) {
      console.error('Error fetching WLP templates:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch WLP templates');
    }
  }

  /**
   * Get a specific WLP template by ID
   * @param {string} templateId - Template ID
   * @returns {Promise<Object>} WLP template data
   */
  async getTemplate(templateId) {
    try {
      const response = await apiClient.get(`${this.endpoint}/${templateId}`, {
        params: {
          userId: apiClient.getCurrentUserId()
        }
      });

      return response.data?.payload || null;
    } catch (error) {
      console.error('Error fetching WLP template:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch WLP template');
    }
  }

  /**
   * Create a new WLP template
   * @param {Object} template - Template data
   * @param {string} template.name - Template name
   * @param {string} [template.description] - Template description
   * @param {string} template.fileName - File name
   * @param {string} template.originalFilename - Original file name
   * @param {ArrayBuffer} template.fileContent - File content
   * @returns {Promise<Object>} Created template data
   */
  async createTemplate(template) {
    try {
      // Convert ArrayBuffer to base64
      const base64Content = this.arrayBufferToBase64(template.fileContent);
      
      const response = await apiClient.post(`${this.endpoint}/create`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: {
          name: template.name,
          description: template.description,
          fileName: template.fileName,
          originalFilename: template.originalFilename,
          fileContent: base64Content
        }
      });

      return response.data?.payload || null;
    } catch (error) {
      console.error('Error creating WLP template:', error);
      throw new Error(error.response?.data?.message || 'Failed to create WLP template');
    }
  }

  /**
   * Update an existing WLP template
   * @param {Object} template - Template data
   * @param {string} template.id - Template ID
   * @param {string} template.name - Template name
   * @param {string} [template.description] - Template description
   * @returns {Promise<Object>} Updated template data
   */
  async updateTemplate(template) {
    try {
      const response = await apiClient.put(`${this.endpoint}/${template.id}`, {
        header: {
          userId: apiClient.getCurrentUserId()
        },
        payload: {
          name: template.name,
          description: template.description
        }
      });

      return response.data?.payload || null;
    } catch (error) {
      console.error('Error updating WLP template:', error);
      throw new Error(error.response?.data?.message || 'Failed to update WLP template');
    }
  }

  /**
   * Delete a WLP template
   * @param {string} templateId - Template ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteTemplate(templateId) {
    try {
      await apiClient.delete(`${this.endpoint}/${templateId}`, {
        params: {
          userId: apiClient.getCurrentUserId()
        }
      });

      return true;
    } catch (error) {
      console.error('Error deleting WLP template:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete WLP template');
    }
  }

  /**
   * Get polygon data for a specific template
   * @param {string} templateId - Template ID
   * @returns {Promise<Array>} Array of polygon data
   */
  async getTemplatePolygons(templateId) {
    try {
      const response = await apiClient.get(`${this.endpoint}/${templateId}/polygons`, {
        params: {
          userId: apiClient.getCurrentUserId()
        }
      });

      return response.data?.payload || [];
    } catch (error) {
      console.error('Error fetching template polygons:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch template polygons');
    }
  }

  /**
   * Convert ArrayBuffer to Base64 string
   * @param {ArrayBuffer} buffer - Array buffer to convert
   * @returns {string} Base64 encoded string
   */
  arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    
    return btoa(binary);
  }

  /**
   * Validate WLP file
   * @param {File} file - File to validate
   * @returns {Object} Validation result
   */
  validateWlpFile(file) {
    const errors = [];
    const warnings = [];

    // Check file extension
    if (!file.name.toLowerCase().endsWith('.wlp')) {
      errors.push('File must have a .wlp extension');
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      errors.push('File size must be less than 10MB');
    }

    // Check if file is empty
    if (file.size === 0) {
      errors.push('File cannot be empty');
    }

    // Check file size warning (5MB)
    if (file.size > 5 * 1024 * 1024) {
      warnings.push('Large file size may take longer to process');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Format file size for display
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted file size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }
}

// Export singleton instance
const wlpTemplateService = new WlpTemplateService();
export default wlpTemplateService;